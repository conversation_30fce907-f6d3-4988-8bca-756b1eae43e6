#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
真实WebSocket数据阻塞问题精确诊断
专门诊断Gate.io和OKX数据流阻塞和期货组合缺失问题
"""

import asyncio
import time
import sys
import os
import json
from pathlib import Path
from datetime import datetime

# 添加项目路径
project_root = Path(__file__).parent.parent / "123"
sys.path.insert(0, str(project_root))

async def diagnose_real_websocket_blocking():
    """诊断真实的WebSocket数据阻塞问题"""
    
    print("🔍 开始真实WebSocket数据阻塞问题诊断")
    print("=" * 80)
    
    diagnosis = {
        "timestamp": datetime.now().isoformat(),
        "gate_issues": [],
        "okx_issues": [],
        "futures_missing": [],
        "log_analysis": {}
    }
    
    try:
        # 1. 检查最新日志文件中的实际错误
        print("\n1️⃣ 分析最新日志中的阻塞警告")
        log_dirs = [
            project_root.parent / "logs",
            project_root / "logs", 
            Path("/tmp/logs"),
            Path("./logs")
        ]
        
        latest_logs = []
        for log_dir in log_dirs:
            if log_dir.exists():
                log_files = list(log_dir.glob("*.log"))
                if log_files:
                    latest_logs.extend(sorted(log_files, key=lambda x: x.stat().st_mtime, reverse=True)[:3])
        
        blocking_warnings = []
        futures_logs = []
        
        for log_file in latest_logs[:5]:  # 检查最新5个日志文件
            try:
                with open(log_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                    
                # 查找阻塞警告
                lines = content.split('\n')
                for line in lines[-1000:]:  # 检查最后1000行
                    if "[SILENT] WARNING" in line and "数据流阻塞" in line:
                        blocking_warnings.append({
                            "file": str(log_file),
                            "line": line.strip(),
                            "time": line.split(' - ')[0] if ' - ' in line else "unknown"
                        })
                    
                    # 查找期货相关日志
                    if "期货" in line or "futures" in line.lower():
                        futures_logs.append({
                            "file": str(log_file),
                            "line": line.strip()
                        })
                        
            except Exception as e:
                print(f"   读取日志文件失败 {log_file}: {e}")
        
        print(f"   发现阻塞警告: {len(blocking_warnings)}条")
        print(f"   发现期货日志: {len(futures_logs)}条")
        
        diagnosis["log_analysis"]["blocking_warnings"] = blocking_warnings[-10:]  # 最新10条
        diagnosis["log_analysis"]["futures_logs"] = futures_logs[-5:]  # 最新5条
        
        # 2. 检查WebSocket客户端实际运行状态
        print("\n2️⃣ 检查WebSocket客户端运行状态")
        
        try:
            from websocket.enhanced_blocking_tracker import get_blocking_tracker
            tracker = get_blocking_tracker()
            
            current_time = time.time()
            active_exchanges = 0
            blocked_exchanges = []
            
            for key, metrics in tracker.exchange_metrics.items():
                gap = current_time - metrics.last_data_time
                if gap > 30:  # 30秒无数据认为阻塞
                    blocked_exchanges.append({
                        "exchange_key": key,
                        "silent_duration": gap,
                        "last_messages": metrics.total_messages,
                        "msg_per_second": metrics.messages_per_second
                    })
                else:
                    active_exchanges += 1
            
            print(f"   活跃交易所: {active_exchanges}")
            print(f"   阻塞交易所: {len(blocked_exchanges)}")
            
            for blocked in blocked_exchanges:
                print(f"   🔴 {blocked['exchange_key']}: 静默{blocked['silent_duration']:.1f}秒")
                
            diagnosis["current_blocking"] = {
                "active_count": active_exchanges,
                "blocked_count": len(blocked_exchanges),
                "blocked_details": blocked_exchanges
            }
            
        except Exception as e:
            print(f"   ❌ 无法检查阻塞追踪器: {e}")
            diagnosis["current_blocking"] = {"error": str(e)}
        
        # 3. 检查期货组合配置和状态
        print("\n3️⃣ 检查期货组合配置")
        
        try:
            from config.settings import TRADING_PAIRS
            trading_pairs = TRADING_PAIRS if hasattr(TRADING_PAIRS, '__call__') else TRADING_PAIRS() if callable(TRADING_PAIRS) else TRADING_PAIRS
            
            print(f"   配置的交易对: {trading_pairs}")
            
            # 检查各交易所期货支持情况
            futures_support = {}
            
            # Gate.io期货检查
            try:
                # 简单检查是否有期货相关配置
                gate_futures_symbols = []
                for symbol in trading_pairs:
                    # Gate期货格式通常是 BTC_USD, ETH_USD
                    if '_' in symbol:
                        gate_futures_symbols.append(symbol)
                
                futures_support["gate"] = {
                    "configured_symbols": gate_futures_symbols,
                    "count": len(gate_futures_symbols)
                }
                
            except Exception as e:
                futures_support["gate"] = {"error": str(e)}
            
            # OKX期货检查  
            try:
                okx_futures_symbols = []
                for symbol in trading_pairs:
                    # OKX期货格式通常是 BTC-USD-SWAP, ETH-USDT-SWAP
                    okx_futures_symbols.append(f"{symbol}-SWAP")
                
                futures_support["okx"] = {
                    "configured_symbols": okx_futures_symbols,
                    "count": len(okx_futures_symbols)
                }
                
            except Exception as e:
                futures_support["okx"] = {"error": str(e)}
            
            # Bybit期货检查
            try:
                bybit_futures_symbols = []
                for symbol in trading_pairs:
                    # Bybit期货格式通常是 BTCUSDT, ETHUSDT  
                    clean_symbol = symbol.replace('-', '').replace('_', '')
                    bybit_futures_symbols.append(clean_symbol)
                
                futures_support["bybit"] = {
                    "configured_symbols": bybit_futures_symbols,
                    "count": len(bybit_futures_symbols)
                }
                
            except Exception as e:
                futures_support["bybit"] = {"error": str(e)}
            
            diagnosis["futures_config"] = futures_support
            
            for exchange, config in futures_support.items():
                if "error" not in config:
                    print(f"   {exchange.upper()}: {config['count']}个期货交易对")
                else:
                    print(f"   {exchange.upper()}: ❌ {config['error']}")
                    
        except Exception as e:
            print(f"   ❌ 无法检查期货配置: {e}")
            diagnosis["futures_config"] = {"error": str(e)}
        
        # 4. 检查实际WebSocket订阅状态
        print("\n4️⃣ 检查WebSocket订阅状态")
        
        try:
            # 尝试创建WebSocket客户端查看订阅状态
            from websocket.gate_ws import GateWebSocketClient
            from websocket.okx_ws import OKXWebSocketClient
            from websocket.bybit_ws import BybitWebSocketClient
            
            # 检查客户端初始化
            clients_status = {}
            
            for name, ClientClass in [("gate", GateWebSocketClient), ("okx", OKXWebSocketClient), ("bybit", BybitWebSocketClient)]:
                try:
                    # 检查现货客户端
                    spot_client = ClientClass("spot")
                    
                    # 检查期货客户端  
                    futures_client = ClientClass("futures")
                    
                    clients_status[name] = {
                        "spot_client": "✅ 可创建",
                        "futures_client": "✅ 可创建",
                        "spot_symbols": getattr(spot_client, 'symbols', [])[:3] if hasattr(spot_client, 'symbols') else [],
                        "futures_symbols": getattr(futures_client, 'symbols', [])[:3] if hasattr(futures_client, 'symbols') else []
                    }
                    
                except Exception as e:
                    clients_status[name] = {"error": str(e)}
            
            diagnosis["websocket_clients"] = clients_status
            
            for exchange, status in clients_status.items():
                if "error" not in status:
                    print(f"   {exchange.upper()}: 现货✅ 期货✅")
                    if status.get("spot_symbols"):
                        print(f"     现货交易对样例: {status['spot_symbols']}")
                    if status.get("futures_symbols"):  
                        print(f"     期货交易对样例: {status['futures_symbols']}")
                else:
                    print(f"   {exchange.upper()}: ❌ {status['error']}")
                    
        except Exception as e:
            print(f"   ❌ 无法检查WebSocket客户端: {e}")
            diagnosis["websocket_clients"] = {"error": str(e)}
        
        # 5. 生成问题摘要
        print("\n5️⃣ 问题摘要分析")
        print("-" * 50)
        
        issues_found = []
        
        # 分析阻塞问题
        if blocking_warnings:
            issues_found.append({
                "type": "数据流阻塞",
                "severity": "HIGH", 
                "count": len(blocking_warnings),
                "description": f"发现{len(blocking_warnings)}条阻塞警告，主要涉及Gate.io和OKX"
            })
        
        # 分析期货组合缺失问题
        if diagnosis.get("current_blocking", {}).get("blocked_count", 0) > 0:
            issues_found.append({
                "type": "实时数据阻塞",
                "severity": "CRITICAL",
                "count": diagnosis["current_blocking"]["blocked_count"],
                "description": f"{diagnosis['current_blocking']['blocked_count']}个交易所数据流当前处于阻塞状态"
            })
        
        # 分析期货配置问题
        futures_issues = []
        for exchange, config in diagnosis.get("futures_config", {}).items():
            if "error" in config:
                futures_issues.append(f"{exchange}期货配置错误")
            elif config.get("count", 0) == 0:
                futures_issues.append(f"{exchange}期货交易对为空")
        
        if futures_issues:
            issues_found.append({
                "type": "期货组合配置问题", 
                "severity": "HIGH",
                "issues": futures_issues,
                "description": f"期货配置存在{len(futures_issues)}个问题"
            })
        
        diagnosis["issues_summary"] = issues_found
        
        print(f"🔍 发现问题数量: {len(issues_found)}")
        for issue in issues_found:
            severity_icon = "🚨" if issue["severity"] == "CRITICAL" else "⚠️" if issue["severity"] == "HIGH" else "ℹ️"
            print(f"   {severity_icon} {issue['type']}: {issue['description']}")
        
        # 保存诊断结果
        result_file = Path(__file__).parent.parent / "diagnostic_results" / f"real_blocking_diagnosis_{int(time.time())}.json"
        result_file.parent.mkdir(exist_ok=True)
        
        with open(result_file, 'w', encoding='utf-8') as f:
            json.dump(diagnosis, f, indent=2, ensure_ascii=False, default=str)
        
        print(f"\n📄 详细诊断结果保存至: {result_file}")
        
        return diagnosis
        
    except Exception as e:
        print(f"❌ 诊断过程异常: {e}")
        import traceback
        traceback.print_exc()
        return {"error": str(e)}

async def main():
    """主函数"""
    result = await diagnose_real_websocket_blocking()
    
    if "error" not in result:
        issues_count = len(result.get("issues_summary", []))
        
        print(f"\n🎯 诊断完成，发现 {issues_count} 个问题类别")
        
        if issues_count > 0:
            print("\n🔧 建议立即采取以下修复措施:")
            print("1. 检查WebSocket连接是否正常建立")
            print("2. 验证期货交易对配置是否正确")  
            print("3. 查看系统日志中的具体错误信息")
            print("4. 测试各交易所API连接状态")
        else:
            print("✅ 未发现明显问题，可能需要更深入的运行时分析")
    else:
        print("❌ 诊断失败，请检查错误信息并重试")

if __name__ == "__main__":
    asyncio.run(main())