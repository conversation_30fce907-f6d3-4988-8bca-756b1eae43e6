#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
WebSocket数据流阻塞精确诊断脚本
针对Gate.io和OKX数据流阻塞问题的专项诊断

基于错误日志分析:
- [okx] 检测到数据流阻塞 | {'silent_duration_seconds': 30.054}  
- [gate] 检测到数据流阻塞 | {'silent_duration_seconds': 30.521}

重点诊断：
1. WebSocket消息接收频率异常
2. 时间戳同步机制问题 
3. 数据流监控逻辑缺陷
4. 连接池管理问题
"""

import asyncio
import time
import json
import logging
import traceback
import sys
import os
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from pathlib import Path

# 添加项目路径 - 修正为123目录
project_root = Path(__file__).parent.parent / "123"
sys.path.insert(0, str(project_root))

class WebSocketBlockingDiagnostic:
    """WebSocket数据流阻塞诊断器"""
    
    def __init__(self):
        self.results = {}
        self.test_start_time = time.time()
        
        # 设置诊断日志
        self.setup_diagnostic_logger()
        
        print("🔍 启动WebSocket数据流阻塞精确诊断...")
        print(f"📅 诊断时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("="*80)
        
    def setup_diagnostic_logger(self):
        """设置诊断专用日志"""
        log_dir = project_root / "diagnostic_results"
        log_dir.mkdir(exist_ok=True)
        
        log_file = log_dir / f"websocket_blocking_diagnosis_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
        
        logging.basicConfig(
            level=logging.DEBUG,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_file, encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        
        self.logger = logging.getLogger(__name__)
        self.logger.info(f"🔍 WebSocket阻塞诊断开始 - 日志文件: {log_file}")
        
    def diagnose_1_timestamp_processor_consistency(self):
        """诊断1: 时间戳处理器一致性检查"""
        print("\n🔍 诊断1: 时间戳处理器一致性检查")
        print("-" * 50)
        
        try:
            # 检查统一时间戳处理器
            from websocket.unified_timestamp_processor import get_timestamp_processor
            
            # 测试三个交易所的时间戳处理器
            exchanges = ['gate', 'okx', 'bybit']
            processor_results = {}
            
            for exchange in exchanges:
                try:
                    processor = get_timestamp_processor(exchange)
                    
                    # 检查关键属性
                    check_result = {
                        'blocking_threshold': getattr(processor, 'blocking_threshold', 'N/A'),
                        'time_offset': getattr(processor, 'time_offset', 'N/A'),
                        'time_synced': getattr(processor, 'time_synced', 'N/A'),
                        'last_sync_time': getattr(processor, 'last_sync_time', 'N/A'),
                        'current_timestamp': processor.get_synced_timestamp({}),
                        'config': getattr(processor, 'config', 'N/A')
                    }
                    
                    processor_results[exchange] = check_result
                    
                    print(f"✅ {exchange.upper()}: 时间戳处理器正常")
                    print(f"   - 阻塞阈值: {check_result['blocking_threshold']}")
                    print(f"   - 时间偏移: {check_result['time_offset']}")
                    print(f"   - 同步状态: {check_result['time_synced']}")
                    
                except Exception as e:
                    print(f"❌ {exchange.upper()}: 时间戳处理器异常 - {e}")
                    processor_results[exchange] = {'error': str(e)}
                    
            self.results['timestamp_processor'] = processor_results
            
        except Exception as e:
            print(f"❌ 时间戳处理器诊断失败: {e}")
            self.results['timestamp_processor'] = {'error': str(e)}
            
    def diagnose_2_websocket_data_flow_monitoring(self):
        """诊断2: WebSocket数据流监控机制检查"""
        print("\n🔍 诊断2: WebSocket数据流监控机制检查")
        print("-" * 50)
        
        try:
            # 检查阻塞追踪器
            from websocket.enhanced_blocking_tracker import get_blocking_tracker
            
            tracker = get_blocking_tracker()
            
            # 检查配置
            config_check = {
                'blocking_threshold': getattr(tracker, 'blocking_threshold', 'N/A'),
                'critical_threshold': getattr(tracker, 'critical_threshold', 'N/A'),
                'exchange_metrics_count': len(getattr(tracker, 'exchange_metrics', {})),
                'active_blocking_events': len(getattr(tracker, 'active_blocking_events', {})),
                'blocking_history_count': len(getattr(tracker, 'blocking_history', []))
            }
            
            print(f"✅ 阻塞追踪器配置:")
            print(f"   - 阻塞阈值: {config_check['blocking_threshold']}秒")
            print(f"   - 严重阈值: {config_check['critical_threshold']}秒")
            print(f"   - 监控交易所数: {config_check['exchange_metrics_count']}")
            print(f"   - 活跃阻塞事件: {config_check['active_blocking_events']}")
            print(f"   - 历史阻塞次数: {config_check['blocking_history_count']}")
            
            # 检查当前交易所状态
            current_metrics = {}
            current_time = time.time()
            
            for key, metrics in getattr(tracker, 'exchange_metrics', {}).items():
                gap = current_time - getattr(metrics, 'last_data_time', 0)
                current_metrics[key] = {
                    'last_data_gap': gap,
                    'total_messages': getattr(metrics, 'total_messages', 0),
                    'messages_per_second': getattr(metrics, 'messages_per_second', 0),
                    'status': 'normal' if gap < 30 else 'potentially_blocked'
                }
                
                status_icon = "🟢" if gap < 30 else "🔴"
                print(f"   {status_icon} {key}: 最后数据{gap:.1f}秒前, {getattr(metrics, 'total_messages', 0)}条消息")
                
            self.results['data_flow_monitoring'] = {
                'config': config_check,
                'current_metrics': current_metrics
            }
            
        except Exception as e:
            print(f"❌ 数据流监控检查失败: {e}")
            self.results['data_flow_monitoring'] = {'error': str(e)}
            
    def diagnose_3_websocket_client_implementation(self):
        """诊断3: WebSocket客户端实现检查"""
        print("\n🔍 诊断3: WebSocket客户端实现检查")
        print("-" * 50)
        
        try:
            # 检查Gate.io和OKX WebSocket客户端
            from websocket.gate_ws import GateWebSocketClient
            from websocket.okx_ws import OKXWebSocketClient
            
            clients_check = {}
            
            # 检查Gate.io客户端
            try:
                gate_spot = GateWebSocketClient("spot")
                gate_futures = GateWebSocketClient("futures")
                
                gate_check = {
                    'spot': self._check_client_attributes(gate_spot),
                    'futures': self._check_client_attributes(gate_futures)
                }
                
                clients_check['gate'] = gate_check
                print(f"✅ Gate.io客户端检查完成")
                
            except Exception as e:
                print(f"❌ Gate.io客户端检查失败: {e}")
                clients_check['gate'] = {'error': str(e)}
                
            # 检查OKX客户端
            try:
                okx_spot = OKXWebSocketClient("spot")
                okx_futures = OKXWebSocketClient("futures")
                
                okx_check = {
                    'spot': self._check_client_attributes(okx_spot),
                    'futures': self._check_client_attributes(okx_futures)
                }
                
                clients_check['okx'] = okx_check
                print(f"✅ OKX客户端检查完成")
                
            except Exception as e:
                print(f"❌ OKX客户端检查失败: {e}")
                clients_check['okx'] = {'error': str(e)}
                
            self.results['websocket_clients'] = clients_check
            
        except Exception as e:
            print(f"❌ WebSocket客户端检查失败: {e}")
            self.results['websocket_clients'] = {'error': str(e)}
            
    def _check_client_attributes(self, client):
        """检查WebSocket客户端关键属性"""
        return {
            'last_data_time': getattr(client, 'last_data_time', 'N/A'),
            'data_flow_timeout': getattr(client, 'data_flow_timeout', 'N/A'),
            'heartbeat_interval': getattr(client, 'heartbeat_interval', 'N/A'),
            'connection_timeout': getattr(client, 'connection_timeout', 'N/A'),
            'integrated_with_pool': getattr(client, '_integrated_with_pool', 'N/A'),
            'pool_connection_id': getattr(client, 'pool_connection_id', 'N/A'),
            'has_timestamp_processor': hasattr(client, 'timestamp_processor'),
            'has_log_data_received': hasattr(client, '_log_data_received')
        }
        
    def diagnose_4_connection_pool_integration(self):
        """诊断4: 连接池集成检查"""
        print("\n🔍 诊断4: 连接池集成检查")
        print("-" * 50)
        
        try:
            from websocket.unified_connection_pool_manager import get_connection_pool_manager
            
            pool_manager = get_connection_pool_manager()
            
            # 检查连接池状态
            pool_status = {
                'active_connections': len(getattr(pool_manager, 'connections', {})),
                'connection_states': {},
                'has_manager': pool_manager is not None
            }
            
            # 检查各交易所连接状态
            exchanges = ['gate', 'okx', 'bybit']
            for exchange in exchanges:
                for market_type in ['spot', 'futures']:
                    connection_id = f"{exchange}_{market_type}_websocket"
                    # 这里可能需要根据实际的连接池管理器API调整
                    # pool_status['connection_states'][connection_id] = pool_manager.get_connection_status(connection_id)
                    
            print(f"✅ 连接池管理器状态:")
            print(f"   - 管理器存在: {pool_status['has_manager']}")
            print(f"   - 活跃连接数: {pool_status['active_connections']}")
            
            self.results['connection_pool'] = pool_status
            
        except Exception as e:
            print(f"❌ 连接池检查失败: {e}")
            self.results['connection_pool'] = {'error': str(e)}
            
    def diagnose_5_log_analysis(self):
        """诊断5: 日志文件分析"""
        print("\n🔍 诊断5: 日志文件分析")
        print("-" * 50)
        
        try:
            log_dir = project_root / "logs"
            if not log_dir.exists():
                print("❌ 日志目录不存在")
                return
                
            # 查找相关日志文件
            today = datetime.now().strftime('%Y%m%d')
            log_files = {
                'silent_disconnect': log_dir / f"websocket_silent_disconnect_{today}.log",
                'blocking': log_dir / f"websocket_blocking_{today}.log",
                'connection': log_dir / f"websocket_connection_{today}.log"
            }
            
            log_analysis = {}
            
            for log_type, log_file in log_files.items():
                if log_file.exists():
                    try:
                        with open(log_file, 'r', encoding='utf-8') as f:
                            lines = f.readlines()
                            
                        # 分析阻塞事件
                        blocking_events = [line for line in lines if '检测到数据流阻塞' in line]
                        
                        log_analysis[log_type] = {
                            'file_exists': True,
                            'total_lines': len(lines),
                            'blocking_events': len(blocking_events),
                            'latest_events': blocking_events[-5:] if blocking_events else []
                        }
                        
                        print(f"✅ {log_type}: {len(lines)}行, {len(blocking_events)}个阻塞事件")
                        
                    except Exception as e:
                        log_analysis[log_type] = {'error': str(e)}
                        print(f"❌ {log_type}: 读取失败 - {e}")
                else:
                    log_analysis[log_type] = {'file_exists': False}
                    print(f"⚠️ {log_type}: 文件不存在")
                    
            self.results['log_analysis'] = log_analysis
            
        except Exception as e:
            print(f"❌ 日志分析失败: {e}")
            self.results['log_analysis'] = {'error': str(e)}
            
    async def diagnose_6_realtime_data_flow_test(self):
        """诊断6: 实时数据流测试"""
        print("\n🔍 诊断6: 实时数据流测试")
        print("-" * 50)
        
        try:
            # 模拟数据流测试
            print("🧪 启动3分钟实时数据流监控测试...")
            
            test_duration = 180  # 3分钟
            test_start = time.time()
            data_points = []
            
            # 获取阻塞追踪器
            from websocket.enhanced_blocking_tracker import get_blocking_tracker
            tracker = get_blocking_tracker()
            
            while time.time() - test_start < test_duration:
                current_time = time.time()
                
                # 检查当前数据流状态
                exchange_status = {}
                for key, metrics in getattr(tracker, 'exchange_metrics', {}).items():
                    gap = current_time - getattr(metrics, 'last_data_time', 0)
                    exchange_status[key] = {
                        'gap': gap,
                        'messages': getattr(metrics, 'total_messages', 0),
                        'status': 'normal' if gap < 30 else 'blocked'
                    }
                    
                data_points.append({
                    'timestamp': current_time,
                    'exchanges': exchange_status
                })
                
                # 显示实时状态
                elapsed = current_time - test_start
                print(f"\r⏱️ 测试进度: {elapsed:.0f}s/{test_duration}s", end='', flush=True)
                
                await asyncio.sleep(5)  # 每5秒检查一次
                
            print(f"\n✅ 实时测试完成，收集了{len(data_points)}个数据点")
            
            # 分析测试结果
            blocked_events = []
            for point in data_points:
                for exchange, status in point['exchanges'].items():
                    if status['status'] == 'blocked':
                        blocked_events.append({
                            'timestamp': point['timestamp'],
                            'exchange': exchange,
                            'gap': status['gap']
                        })
                        
            self.results['realtime_test'] = {
                'test_duration': test_duration,
                'data_points': len(data_points),
                'blocked_events': len(blocked_events),
                'blocked_details': blocked_events[-10:]  # 最近10个阻塞事件
            }
            
            if blocked_events:
                print(f"⚠️ 测试期间发现{len(blocked_events)}次数据流阻塞")
            else:
                print("✅ 测试期间未发现数据流阻塞")
                
        except Exception as e:
            print(f"❌ 实时测试失败: {e}")
            self.results['realtime_test'] = {'error': str(e)}
            
    def generate_diagnosis_report(self):
        """生成诊断报告"""
        print("\n" + "="*80)
        print("📋 WebSocket数据流阻塞诊断报告")
        print("="*80)
        
        # 保存详细结果
        report_dir = project_root / "diagnostic_results"
        report_dir.mkdir(exist_ok=True)
        
        report_file = report_dir / f"websocket_blocking_diagnosis_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        # 添加总结信息
        self.results['diagnosis_summary'] = {
            'test_start_time': self.test_start_time,
            'test_end_time': time.time(),
            'test_duration': time.time() - self.test_start_time,
            'total_checks': len(self.results),
            'failed_checks': len([k for k, v in self.results.items() if isinstance(v, dict) and 'error' in v])
        }
        
        # 保存JSON报告
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(self.results, f, indent=2, ensure_ascii=False, default=str)
            
        print(f"📄 详细报告已保存: {report_file}")
        
        # 控制台总结
        print(f"\n🎯 诊断总结:")
        print(f"   - 测试持续时间: {self.results['diagnosis_summary']['test_duration']:.1f}秒")
        print(f"   - 完成检查项: {self.results['diagnosis_summary']['total_checks']}")
        print(f"   - 失败检查项: {self.results['diagnosis_summary']['failed_checks']}")
        
        # 关键发现
        print(f"\n🔍 关键发现:")
        
        # 检查时间戳处理器问题
        if 'timestamp_processor' in self.results:
            tp_result = self.results['timestamp_processor']
            if any('error' in str(v) for v in tp_result.values()):
                print("❌ 时间戳处理器存在异常")
            else:
                print("✅ 时间戳处理器正常")
                
        # 检查数据流监控问题
        if 'data_flow_monitoring' in self.results:
            dfm_result = self.results['data_flow_monitoring']
            if 'error' in dfm_result:
                print("❌ 数据流监控机制异常")
            else:
                blocked_exchanges = [k for k, v in dfm_result.get('current_metrics', {}).items() 
                                   if v.get('status') == 'potentially_blocked']
                if blocked_exchanges:
                    print(f"⚠️ 检测到可能阻塞的交易所: {', '.join(blocked_exchanges)}")
                else:
                    print("✅ 当前无交易所数据流阻塞")
                    
        # 检查实时测试结果
        if 'realtime_test' in self.results:
            rt_result = self.results['realtime_test']
            if 'error' not in rt_result:
                blocked_count = rt_result.get('blocked_events', 0)
                if blocked_count > 0:
                    print(f"⚠️ 实时测试发现{blocked_count}次阻塞事件")
                else:
                    print("✅ 实时测试期间无阻塞事件")
                    
        print("\n" + "="*80)

async def main():
    """主诊断函数"""
    diagnostic = WebSocketBlockingDiagnostic()
    
    try:
        # 依次执行各项诊断
        diagnostic.diagnose_1_timestamp_processor_consistency()
        diagnostic.diagnose_2_websocket_data_flow_monitoring()
        diagnostic.diagnose_3_websocket_client_implementation()
        diagnostic.diagnose_4_connection_pool_integration()
        diagnostic.diagnose_5_log_analysis()
        
        # 实时测试需要异步执行
        await diagnostic.diagnose_6_realtime_data_flow_test()
        
        # 生成诊断报告
        diagnostic.generate_diagnosis_report()
        
    except KeyboardInterrupt:
        print("\n\n⏹️ 诊断被用户中断")
    except Exception as e:
        print(f"\n❌ 诊断过程异常: {e}")
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(main())