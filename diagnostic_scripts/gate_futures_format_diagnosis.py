#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Gate期货数据格式诊断脚本
精确诊断Gate期货订单簿数据解析失败的根本原因
"""

import asyncio
import time
import sys
import json
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent.parent / "123"
sys.path.insert(0, str(project_root))

async def diagnose_gate_futures_format():
    """诊断Gate期货数据格式问题"""
    
    print("🔍 开始Gate期货数据格式诊断")
    print("=" * 80)
    
    try:
        # 1. 创建测试WebSocket客户端
        from websocket.gate_ws import GateWebSocketClient
        
        client = GateWebSocketClient("futures")
        
        # 2. Hook到数据处理函数，记录原始数据
        original_handle_orderbook = client._handle_orderbook
        received_data = []
        
        async def debug_handle_orderbook(data):
            """调试版的订单簿处理函数"""
            print(f"\n📋 接收到Gate期货原始数据:")
            print(f"   数据类型: {type(data)}")
            print(f"   数据keys: {list(data.keys()) if isinstance(data, dict) else 'Not dict'}")
            
            # 保存原始数据用于分析
            received_data.append({
                "timestamp": time.time(),
                "data": data,
                "type": str(type(data))
            })
            
            # 详细分析数据结构
            if isinstance(data, dict):
                print(f"   🔍 详细数据结构分析:")
                
                # 检查asks格式
                asks = data.get("asks", [])
                if asks:
                    print(f"   📈 asks数据:")
                    print(f"      asks类型: {type(asks)}")
                    print(f"      asks长度: {len(asks)}")
                    if len(asks) > 0:
                        first_ask = asks[0]
                        print(f"      第一个ask: {first_ask}")
                        print(f"      第一个ask类型: {type(first_ask)}")
                        if isinstance(first_ask, dict):
                            print(f"      第一个ask keys: {list(first_ask.keys())}")
                        
                # 检查bids格式
                bids = data.get("bids", [])
                if bids:
                    print(f"   📉 bids数据:")
                    print(f"      bids类型: {type(bids)}")
                    print(f"      bids长度: {len(bids)}")
                    if len(bids) > 0:
                        first_bid = bids[0]
                        print(f"      第一个bid: {first_bid}")
                        print(f"      第一个bid类型: {type(first_bid)}")
                        if isinstance(first_bid, dict):
                            print(f"      第一个bid keys: {list(first_bid.keys())}")
            
            print(f"   💾 数据样本: {str(data)[:300]}...")
            
            # 调用原始处理函数查看是否成功
            try:
                await original_handle_orderbook(data)
                print(f"   ✅ 原始处理函数执行成功")
            except Exception as e:
                print(f"   ❌ 原始处理函数执行失败: {e}")
                import traceback
                traceback.print_exc()
            
            return data
        
        # 替换处理函数
        client._handle_orderbook = debug_handle_orderbook
        
        # 3. 模拟连接和接收数据
        print("\n🔌 模拟Gate期货WebSocket连接...")
        
        # 创建测试数据（基于可能的格式）
        test_data_formats = [
            # 格式1: 标准数组格式
            {
                "currency_pair": "ADA_USDT", 
                "asks": [["0.74", "100"], ["0.75", "200"]], 
                "bids": [["0.73", "150"], ["0.72", "250"]]
            },
            # 格式2: 字典格式
            {
                "currency_pair": "ADA_USDT",
                "asks": [{"p": "0.74", "s": "100"}, {"p": "0.75", "s": "200"}],
                "bids": [{"p": "0.73", "s": "150"}, {"p": "0.72", "s": "250"}]
            },
            # 格式3: 嵌套格式
            {
                "s": "ADA_USDT",
                "result": {
                    "asks": [["0.74", "100"], ["0.75", "200"]], 
                    "bids": [["0.73", "150"], ["0.72", "250"]]
                }
            },
            # 格式4: Gate期货特殊格式
            {
                "currency_pair": "ADA_USDT",
                "a": [["0.74", "100"], ["0.75", "200"]],  # a = asks
                "b": [["0.73", "150"], ["0.72", "250"]]   # b = bids
            }
        ]
        
        # 测试不同数据格式
        for i, test_data in enumerate(test_data_formats):
            print(f"\n📊 测试数据格式 {i+1}:")
            await debug_handle_orderbook(test_data)
            await asyncio.sleep(1)
        
        # 4. 检查实际的WebSocket消息日志
        print(f"\n📋 查找实际WebSocket消息日志...")
        
        log_files = []
        for log_dir in [project_root / "logs", project_root.parent / "logs"]:
            if log_dir.exists():
                log_files.extend(list(log_dir.glob("*gate*.log")))
                log_files.extend(list(log_dir.glob("*websocket*.log")))
        
        # 查找最近的消息示例
        recent_messages = []
        for log_file in log_files[-3:]:  # 最近3个日志文件
            try:
                with open(log_file, 'r', encoding='utf-8') as f:
                    lines = f.readlines()[-500:]  # 最后500行
                    
                for line in lines:
                    if "Gate快照数据不完整" in line or "asks=0 bids=0" in line:
                        recent_messages.append({
                            "file": str(log_file),
                            "line": line.strip(),
                            "timestamp": "recent"
                        })
                        
            except Exception as e:
                print(f"   读取日志失败 {log_file}: {e}")
        
        print(f"   发现相关日志消息: {len(recent_messages)}条")
        for msg in recent_messages[-5:]:  # 显示最近5条
            print(f"   📄 {msg['line']}")
        
        # 5. 生成诊断报告
        diagnosis = {
            "timestamp": time.time(),
            "received_data_count": len(received_data),
            "test_formats_count": len(test_data_formats),
            "log_messages": recent_messages,
            "diagnosis_summary": {
                "problem": "Gate期货数据解析asks=0 bids=0",
                "possible_causes": [
                    "数据格式不匹配现有解析逻辑",
                    "字段名称不一致（如使用'a'/'b'而非'asks'/'bids'）",
                    "数据类型转换问题",
                    "嵌套结构解析错误"
                ],
                "next_steps": [
                    "检查实际WebSocket消息格式",
                    "添加更多数据格式支持",
                    "增强错误日志记录",
                    "测试修复后的解析逻辑"
                ]
            }
        }
        
        # 保存诊断结果
        result_file = Path(__file__).parent.parent / "diagnostic_results" / f"gate_futures_format_diagnosis_{int(time.time())}.json"
        result_file.parent.mkdir(exist_ok=True)
        
        with open(result_file, 'w', encoding='utf-8') as f:
            json.dump(diagnosis, f, indent=2, ensure_ascii=False, default=str)
        
        print(f"\n📄 诊断报告已保存: {result_file}")
        
        return diagnosis
        
    except Exception as e:
        print(f"❌ 诊断过程异常: {e}")
        import traceback
        traceback.print_exc()
        return {"error": str(e)}

async def main():
    """主函数"""
    result = await diagnose_gate_futures_format()
    
    if "error" not in result:
        print(f"\n🎯 诊断完成")
        print(f"📊 数据样本数量: {result.get('received_data_count', 0)}")
        print(f"🧪 测试格式数量: {result.get('test_formats_count', 0)}")
        print(f"📄 相关日志消息: {len(result.get('log_messages', []))}")
        
        print(f"\n🔧 建议下一步:")
        for step in result.get('diagnosis_summary', {}).get('next_steps', []):
            print(f"   - {step}")
    else:
        print("❌ 诊断失败，请检查错误信息")

if __name__ == "__main__":
    asyncio.run(main())