#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试Gate期货数据格式解析修复
验证修复后的解析逻辑是否能正确处理各种数据格式
"""

import asyncio
import time
import sys
import json
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent.parent / "123"
sys.path.insert(0, str(project_root))

async def test_gate_futures_parsing_fix():
    """测试Gate期货数据解析修复"""
    
    print("🧪 测试Gate期货数据格式解析修复")
    print("=" * 80)
    
    try:
        from websocket.gate_ws import GateWebSocketClient
        
        # 创建期货客户端
        client = GateWebSocketClient("futures")
        
        # 测试数据格式1：标准格式 - 增加深度数量
        test_data_1 = {
            "currency_pair": "ADA_USDT", 
            "asks": [["0.7450", "1000"], ["0.7451", "2000"], ["0.7452", "1500"], ["0.7453", "3000"], ["0.7454", "2500"]], 
            "bids": [["0.7449", "1500"], ["0.7448", "2500"], ["0.7447", "1800"], ["0.7446", "3200"], ["0.7445", "2800"]],
            "t": int(time.time() * 1000)
        }
        
        # 测试数据格式2：字典格式 - 增加深度数量
        test_data_2 = {
            "currency_pair": "ADA_USDT",
            "asks": [{"p": "0.7450", "s": "1000"}, {"p": "0.7451", "s": "2000"}, {"p": "0.7452", "s": "1500"}, {"p": "0.7453", "s": "3000"}, {"p": "0.7454", "s": "2500"}],
            "bids": [{"p": "0.7449", "s": "1500"}, {"p": "0.7448", "s": "2500"}, {"p": "0.7447", "s": "1800"}, {"p": "0.7446", "s": "3200"}, {"p": "0.7445", "s": "2800"}],
            "t": int(time.time() * 1000)
        }
        
        # 测试数据格式3：使用'a'/'b'字段 - 增加深度数量
        test_data_3 = {
            "currency_pair": "ADA_USDT",
            "a": [["0.7450", "1000"], ["0.7451", "2000"], ["0.7452", "1500"], ["0.7453", "3000"], ["0.7454", "2500"]],  # a = asks
            "b": [["0.7449", "1500"], ["0.7448", "2500"], ["0.7447", "1800"], ["0.7446", "3200"], ["0.7445", "2800"]],  # b = bids
            "t": int(time.time() * 1000)
        }
        
        # 测试数据格式4：嵌套在result中 - 增加深度数量
        test_data_4 = {
            "result": {
                "currency_pair": "ADA_USDT",
                "asks": [["0.7450", "1000"], ["0.7451", "2000"], ["0.7452", "1500"], ["0.7453", "3000"], ["0.7454", "2500"]],
                "bids": [["0.7449", "1500"], ["0.7448", "2500"], ["0.7447", "1800"], ["0.7446", "3200"], ["0.7445", "2800"]],
                "t": int(time.time() * 1000)
            }
        }
        
        # 测试数据格式5：嵌套result + 'a'/'b'字段 - 增加深度数量
        test_data_5 = {
            "result": {
                "currency_pair": "ADA_USDT",
                "a": [["0.7450", "1000"], ["0.7451", "2000"], ["0.7452", "1500"], ["0.7453", "3000"], ["0.7454", "2500"]],
                "b": [["0.7449", "1500"], ["0.7448", "2500"], ["0.7447", "1800"], ["0.7446", "3200"], ["0.7445", "2800"]],
                "t": int(time.time() * 1000)
            }
        }
        
        test_cases = [
            ("标准asks/bids格式", test_data_1),
            ("字典格式", test_data_2),  
            ("a/b字段格式", test_data_3),
            ("嵌套result+asks/bids", test_data_4),
            ("嵌套result+a/b", test_data_5)
        ]
        
        # 设置测试交易对
        client.set_symbols(["ADA_USDT"])
        
        # 测试结果统计
        success_count = 0
        total_count = len(test_cases)
        
        # 记录生成的market_data事件
        market_data_events = []
        
        def capture_market_data(data):
            """捕获market_data事件"""
            market_data_events.append(data)
            print(f"✅ 生成market_data事件: {data['symbol']} asks={len(data.get('asks', []))} bids={len(data.get('bids', []))}")
        
        client.register_callback("market_data", capture_market_data)
        
        # 测试各种数据格式
        for i, (desc, test_data) in enumerate(test_cases):
            print(f"\n📊 测试 {i+1}/{total_count}: {desc}")
            print(f"   输入数据: {json.dumps(test_data, ensure_ascii=False)[:100]}...")
            
            try:
                # 清空之前的事件
                market_data_events.clear()
                
                # 处理测试数据
                await client._handle_orderbook(test_data)
                
                # 检查是否生成了market_data事件
                if market_data_events:
                    success_count += 1
                    event = market_data_events[0]
                    print(f"   ✅ 成功: 生成了market_data事件")
                    print(f"      - asks数量: {len(event.get('asks', []))}")
                    print(f"      - bids数量: {len(event.get('bids', []))}")
                    print(f"      - 时间戳: {event.get('timestamp')}")
                else:
                    print(f"   ❌ 失败: 未生成market_data事件")
                    
            except Exception as e:
                print(f"   ❌ 异常: {e}")
        
        # 输出测试结果
        print(f"\n🎯 测试结果摘要:")
        print(f"   总测试数: {total_count}")
        print(f"   成功数: {success_count}")
        print(f"   成功率: {success_count/total_count*100:.1f}%")
        
        if success_count == total_count:
            print(f"   🎉 全部通过！Gate期货数据解析修复成功")
        elif success_count > 0:
            print(f"   ⚠️ 部分通过，还需要进一步优化")
        else:
            print(f"   ❌ 全部失败，需要重新检查修复逻辑")
        
        return {
            "total": total_count,
            "success": success_count,
            "success_rate": success_count/total_count*100,
            "all_passed": success_count == total_count
        }
        
    except Exception as e:
        print(f"❌ 测试过程异常: {e}")
        import traceback
        traceback.print_exc()
        return {"error": str(e)}

async def main():
    """主函数"""
    print("🔧 Gate期货数据解析修复验证测试")
    print("目标：确认修复后的解析逻辑能处理所有可能的数据格式")
    print("=" * 80)
    
    result = await test_gate_futures_parsing_fix()
    
    if "error" not in result:
        print(f"\n📋 测试完成")
        if result["all_passed"]:
            print("🚀 修复验证成功！Gate期货数据解析问题已解决")
            print("💡 建议：重启系统并观察Gate期货是否正常生成market_data事件")
        else:
            print("⚠️ 修复需要进一步优化")
    else:
        print("❌ 测试失败，请检查错误信息")

if __name__ == "__main__":
    asyncio.run(main())