#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
WebSocket数据流阻塞修复验证脚本
验证统一阻塞检测机制是否正常工作

修复要点验证：
1. WebSocket客户端正确调用阻塞追踪器
2. 时间戳处理器与阻塞追踪器协调工作
3. 不再出现重复的阻塞检测日志
"""

import asyncio
import time
import sys
import os
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent.parent / "123"
sys.path.insert(0, str(project_root))

async def test_websocket_blocking_fix():
    """测试WebSocket阻塞修复效果"""
    
    print("🧪 开始WebSocket数据流阻塞修复验证...")
    print("=" * 60)
    
    try:
        # 1. 测试阻塞追踪器
        print("\n1️⃣ 测试阻塞追踪器功能")
        from websocket.enhanced_blocking_tracker import get_blocking_tracker, log_websocket_data_received
        
        tracker = get_blocking_tracker()
        initial_count = len(tracker.exchange_metrics)
        print(f"   初始监控交易所数: {initial_count}")
        
        # 模拟数据接收
        log_websocket_data_received("gate", "spot", "BTC-USDT", {"test": "data1"})
        log_websocket_data_received("okx", "futures", "ETH-USDT", {"test": "data2"})
        log_websocket_data_received("bybit", "spot", "SOL-USDT", {"test": "data3"})
        
        after_count = len(tracker.exchange_metrics)
        print(f"   模拟后监控交易所数: {after_count}")
        print(f"   ✅ 阻塞追踪器正常工作: {after_count > initial_count}")
        
        # 2. 测试WebSocket客户端集成
        print("\n2️⃣ 测试WebSocket客户端集成")
        from websocket.gate_ws import GateWebSocketClient
        from websocket.okx_ws import OKXWebSocketClient
        from websocket.bybit_ws import BybitWebSocketClient
        
        # 创建客户端实例
        gate_client = GateWebSocketClient("spot")
        okx_client = OKXWebSocketClient("spot") 
        bybit_client = BybitWebSocketClient("spot")
        
        # 检查客户端配置
        clients = [
            ("Gate", gate_client),
            ("OKX", okx_client), 
            ("Bybit", bybit_client)
        ]
        
        for name, client in clients:
            has_tracker = hasattr(client, '_log_data_received')
            has_timestamp_processor = hasattr(client, 'timestamp_processor')
            print(f"   {name}: 追踪器={has_tracker}, 时间戳处理器={has_timestamp_processor}")
            
        # 3. 测试时间戳处理器协调
        print("\n3️⃣ 测试时间戳处理器协调")
        from websocket.unified_timestamp_processor import get_timestamp_processor
        
        for exchange in ["gate", "okx", "bybit"]:
            processor = get_timestamp_processor(exchange)
            
            # 测试时间戳同步
            test_data = {"timestamp": int(time.time() * 1000)}
            synced_timestamp = processor.get_synced_timestamp(test_data)
            
            print(f"   {exchange.upper()}: 时间戳同步正常 (输出: {synced_timestamp})")
            
        # 4. 测试数据流阻塞检测协调
        print("\n4️⃣ 测试数据流阻塞检测协调")
        
        # 模拟正常数据流
        print("   模拟正常数据流...")
        for i in range(3):
            log_websocket_data_received("gate", "spot", "BTC-USDT", {"seq": i})
            log_websocket_data_received("okx", "futures", "ETH-USDT", {"seq": i}) 
            log_websocket_data_received("bybit", "spot", "SOL-USDT", {"seq": i})
            await asyncio.sleep(1)
            
        # 检查追踪器状态
        current_time = time.time()
        blocked_exchanges = []
        
        for key, metrics in tracker.exchange_metrics.items():
            gap = current_time - metrics.last_data_time
            if gap > 30:  # 30秒阈值
                blocked_exchanges.append(key)
                
        if blocked_exchanges:
            print(f"   ⚠️ 检测到阻塞交易所: {blocked_exchanges}")
        else:
            print("   ✅ 所有交易所数据流正常")
            
        # 5. 生成报告
        print("\n5️⃣ 修复验证报告")
        print("-" * 40)
        
        total_exchanges = len(tracker.exchange_metrics)
        total_messages = sum(m.total_messages for m in tracker.exchange_metrics.values())
        
        print(f"   📊 监控交易所总数: {total_exchanges}")
        print(f"   📨 总消息数: {total_messages}")
        print(f"   🔄 阻塞事件数: {len(tracker.blocking_history)}")
        
        # 详细状态
        for key, metrics in tracker.exchange_metrics.items():
            gap = current_time - metrics.last_data_time
            status = "🟢 正常" if gap < 10 else "🔴 可能阻塞"
            print(f"   {key}: {status} (最后数据: {gap:.1f}秒前, 消息: {metrics.total_messages})")
            
        print(f"\n✅ WebSocket数据流阻塞修复验证完成！")
        
        # 返回验证结果
        return {
            "tracking_working": after_count > initial_count,
            "clients_integrated": all(hasattr(c, '_log_data_received') for _, c in clients),
            "timestamp_sync_working": True,  # 如果到这里说明时间戳同步正常
            "no_blocked_exchanges": len(blocked_exchanges) == 0,
            "total_monitored_exchanges": total_exchanges,
            "total_messages": total_messages
        }
        
    except Exception as e:
        print(f"❌ 验证过程出错: {e}")
        import traceback
        traceback.print_exc()
        return {"error": str(e)}

async def main():
    """主函数"""
    result = await test_websocket_blocking_fix()
    
    if "error" not in result:
        success_count = sum(1 for v in result.values() if v is True)
        total_checks = len([k for k in result.keys() if isinstance(result[k], bool)])
        
        print(f"\n🎯 验证结果: {success_count}/{total_checks} 项检查通过")
        
        if success_count == total_checks:
            print("🎉 所有检查通过，修复成功！")
        else:
            print("⚠️ 部分检查未通过，需要进一步调试")
    else:
        print("❌ 验证失败，请检查错误信息")

if __name__ == "__main__":
    asyncio.run(main())