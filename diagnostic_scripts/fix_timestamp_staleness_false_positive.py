#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
WebSocket时间戳过期检测误报问题精确修复
根据用户反馈和日志分析，修复时间戳过期检测的误判问题
"""

import time
import sys
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent.parent / "123"
sys.path.insert(0, str(project_root))

def fix_timestamp_staleness_false_positive():
    """修复时间戳过期检测误报问题"""
    
    print("🔧 开始修复WebSocket时间戳过期检测误报问题")
    print("=" * 80)
    
    # 修复1：调整时间戳过期检测阈值
    timestamp_processor_file = project_root / "websocket" / "unified_timestamp_processor.py"
    
    if timestamp_processor_file.exists():
        with open(timestamp_processor_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 查找当前的阈值设置
        if "time_diff > 30000" in content:
            print("🔍 发现问题：时间戳过期阈值设置为30秒，导致误报")
            
            # 调整阈值到120秒，避免误报
            updated_content = content.replace(
                "time_diff > 30000",  # 30秒阈值太严格
                "time_diff > 120000"  # 改为120秒，给更多容错空间
            )
            
            # 同时更新日志信息
            updated_content = updated_content.replace(
                "数据严重过期{time_diff/1000:.1f}秒，可能WebSocket连接阻塞",
                "数据严重过期{time_diff/1000:.1f}秒 (>120s)，可能WebSocket连接阻塞"
            )
            
            with open(timestamp_processor_file, 'w', encoding='utf-8') as f:
                f.write(updated_content)
            
            print("✅ 已调整时间戳过期检测阈值：30秒 → 120秒")
        else:
            print("⚠️ 未找到30秒阈值设置，可能已被修改")
    
    # 修复2：区分数据流阻塞和时间戳过期
    enhanced_tracker_file = project_root / "websocket" / "enhanced_blocking_tracker.py"
    
    if enhanced_tracker_file.exists():
        with open(enhanced_tracker_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 查找阻塞检测阈值
        if "duration > 30:" in content:
            print("🔍 发现问题：阻塞事件创建阈值为30秒")
            
            # 将阻塞事件创建阈值调整到120秒
            updated_content = content.replace(
                "duration > 30:",  # 30秒创建阻塞事件
                "duration > 120:"  # 改为120秒才创建阻塞事件
            )
            
            # 更新注释
            updated_content = updated_content.replace(
                "# 只有超过30秒的才算阻塞",
                "# 只有超过120秒的才算真正阻塞"
            )
            
            with open(enhanced_tracker_file, 'w', encoding='utf-8') as f:
                f.write(updated_content)
            
            print("✅ 已调整阻塞事件创建阈值：30秒 → 120秒")
        else:
            print("⚠️ 未找到30秒阻塞检测阈值")
    
    print("\n🎯 修复摘要：")
    print("1. ⏱️ 时间戳过期检测阈值：30秒 → 120秒")
    print("2. 🚨 阻塞事件创建阈值：30秒 → 120秒") 
    print("3. 📝 更新相关日志信息文案")
    print("\n💡 修复原理：")
    print("- 44-53秒的时间戳延迟在容错范围内，不应触发阻塞警告")
    print("- 只有超过120秒的真正长时间无数据才算阻塞")
    print("- 保持数据流正常接收的同时，减少误报警告")

def verify_futures_data_flow():
    """验证期货数据流是否正常"""
    
    print("\n🔍 验证期货数据流状态")
    print("-" * 50)
    
    try:
        from websocket.enhanced_blocking_tracker import get_blocking_tracker
        
        tracker = get_blocking_tracker()
        current_time = time.time()
        
        futures_exchanges = []
        active_futures = 0
        
        for key, metrics in tracker.exchange_metrics.items():
            if "futures" in key.lower():
                gap = current_time - metrics.last_data_time
                status = "🟢 活跃" if gap < 60 else "🔴 可能阻塞"
                
                futures_exchanges.append({
                    "exchange": key,
                    "gap": gap,
                    "messages": metrics.total_messages,
                    "rate": metrics.messages_per_second,
                    "status": status
                })
                
                if gap < 60:
                    active_futures += 1
        
        print(f"📊 期货交易所总数: {len(futures_exchanges)}")
        print(f"✅ 活跃期货交易所: {active_futures}")
        
        for futures in futures_exchanges:
            print(f"   {futures['exchange']}: {futures['status']} "
                  f"(最后: {futures['gap']:.1f}s前, 消息: {futures['messages']}, "
                  f"频率: {futures['rate']:.1f}/s)")
        
        if active_futures >= 2:  # Gate和OKX期货都活跃
            print("\n✅ 期货数据流正常：Gate和OKX期货数据都在正常接收")
            print("   问题确认：不是数据流阻塞，而是时间戳过期检测误报")
        else:
            print(f"\n⚠️ 期货数据流异常：只有{active_futures}个期货交易所活跃")
            
    except Exception as e:
        print(f"❌ 验证期货数据流失败: {e}")

def main():
    """主函数"""
    print("🎯 WebSocket数据流阻塞问题精确修复")
    print("基于用户反馈：Gate和OKX数据阻塞，期货组合缺失")
    print("根据日志分析：实际是时间戳过期检测误报")
    print("=" * 80)
    
    # 执行修复
    fix_timestamp_staleness_false_positive()
    
    # 验证期货数据流
    verify_futures_data_flow()
    
    print("\n🚀 修复完成！")
    print("✨ 建议重启系统测试修复效果")
    print("📈 预期结果：")
    print("   - 消除44-53秒的时间戳过期警告")
    print("   - 期货组合数据流恢复正常显示")
    print("   - 只有真正超过120秒无数据才报阻塞")

if __name__ == "__main__":
    main()