{"$schema": "https://json.schemastore.org/claude-code-settings.json", "permissions": {"allow": ["Bash(find:*)", "Bash(rg:*)", "Bash(grep:*)", "Bash(python diagnostic_scripts/timestamp_sync_diagnosis.py:*)", "<PERSON><PERSON>(python3:*)", "<PERSON><PERSON>(python:*)", "Bash(timeout 60 python3 diagnostic_scripts/test_prod_consistency_validator.py)", "WebFetch(domain:www.gate.io)", "WebFetch(domain:www.gate.com)", "WebFetch(domain:www.okx.com)", "Bash(ls:*)", "<PERSON><PERSON>(cat:*)", "<PERSON><PERSON>(timeout:*)", "Bash(rm:*)", "Bash(kill:*)", "WebFetch(domain:bybit-exchange.github.io)", "<PERSON><PERSON>(mkdir:*)", "WebFetch(domain:github.com)", "<PERSON><PERSON>(pkill:*)"], "deny": []}}