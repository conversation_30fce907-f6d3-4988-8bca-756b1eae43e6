2025-08-05 11:29:12.206 [INFO] [exchanges.okx_exchange] ✅ OKX API限速使用统一配置: 1次/秒
2025-08-05 11:29:12 [DEBUG] [OKXExchange] ✅ OKX统一模块初始化完成
2025-08-05 11:29:12 [DEBUG] [OKXExchange] ✅ OKX配置加载完成: 杠杆=3x
2025-08-05 11:29:12 [INFO] [OKXExchange] ✅ OKX交易所统一初始化完成 - 🔥 零重复逻辑
2025-08-05 11:29:12 [DEBUG] [OKXExchange] ✅ OKX统一属性设置完成 - trading_rules_preloader已正确设置
2025-08-05 11:29:12.206 [INFO] [exchanges.okx_exchange] OKX交易所初始化完成: 统一账户模式, 默认杠杆=3倍, API限制=1次/秒
2025-08-05 11:29:12 [INFO] [OKXExchange] 🔥 开始初始化OKX账户配置...
2025-08-05 11:29:12 [INFO] [OKXExchange] OKX当前账户模式: 2
2025-08-05 11:29:12 [WARNING] [OKXExchange] OKX账户不是跨币种保证金模式，当前模式: 2
2025-08-05 11:29:12 [INFO] [OKXExchange] OKX设置为单向持仓模式
2025-08-05 11:29:12.822 [INFO] [exchanges.okx_exchange] OKX设置杠杆: ADA-USDT-SWAP 3倍，保证金模式: cross
2025-08-05 11:29:13.334 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-08-05 11:29:13.334 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-05 11:29:13.883 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-08-05 11:29:13.883 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-08-05 11:29:13 [DEBUG] [OKXExchange] OKX预设置杠杆成功: ADA-USDT
2025-08-05 11:29:13.883 [INFO] [exchanges.okx_exchange] OKX设置杠杆: DOGE-USDT-SWAP 3倍，保证金模式: cross
2025-08-05 11:29:14.326 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-08-05 11:29:14.326 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-05 11:29:14.820 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-08-05 11:29:14.820 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-08-05 11:29:14 [DEBUG] [OKXExchange] OKX预设置杠杆成功: DOGE-USDT
2025-08-05 11:29:14.820 [INFO] [exchanges.okx_exchange] OKX设置杠杆: SOL-USDT-SWAP 3倍，保证金模式: cross
2025-08-05 11:29:15.315 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-08-05 11:29:15.316 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-05 11:29:15.829 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-08-05 11:29:15.829 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-08-05 11:29:15 [DEBUG] [OKXExchange] OKX预设置杠杆成功: SOL-USDT
2025-08-05 11:29:15.829 [INFO] [exchanges.okx_exchange] OKX设置杠杆: AVAX-USDT-SWAP 3倍，保证金模式: cross
2025-08-05 11:29:16.315 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-08-05 11:29:16.315 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-05 11:29:16.822 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-08-05 11:29:16.822 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-08-05 11:29:16 [DEBUG] [OKXExchange] OKX预设置杠杆成功: AVAX-USDT
2025-08-05 11:29:16 [INFO] [OKXExchange] ✅ OKX账户初始化完成
2025-08-05 11:29:17.317 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: ADA-USDT -> 最大杠杆=50x
2025-08-05 11:29:17.328 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: SOL-USDT -> 最大杠杆=50x
2025-08-05 11:29:17.355 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: AVAX-USDT -> 最大杠杆=50x
2025-08-05 11:29:17.356 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: DOGE-USDT -> 最大杠杆=50x
2025-08-05 11:29:17.357 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: SHIB-USDT -> 最大杠杆=50x
2025-08-05 11:29:17.371 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: DOT-USDT -> 最大杠杆=50x
2025-08-05 11:29:17.374 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: BNB-USDT -> 最大杠杆=50x
2025-08-05 11:29:42.571 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-08-05 11:29:42.571 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-05 11:29:43.582 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-08-05 11:29:43.582 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-05 11:29:44.580 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-08-05 11:29:44.580 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-05 11:29:45.586 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-08-05 11:29:45.586 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-05 11:29:46.577 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-08-05 11:29:46.578 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-05 11:29:47.577 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-08-05 11:29:47.577 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-05 11:30:30.630 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: ADA-USDT -> 最大杠杆=50x
2025-08-05 11:30:35.712 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: DOGE-USDT -> 最大杠杆=50x
2025-08-05 11:30:37.263 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: SOL-USDT -> 最大杠杆=50x
2025-08-05 11:30:37.705 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: DOT-USDT -> 最大杠杆=50x
2025-08-05 11:30:37.718 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: AVAX-USDT -> 最大杠杆=50x
2025-08-05 11:30:37.729 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: SHIB-USDT -> 最大杠杆=50x
2025-08-05 11:30:39.796 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: BNB-USDT -> 最大杠杆=50x
2025-08-05 11:30:41.231 [INFO] [exchanges.okx_exchange] OKX设置杠杆: ADA-USDT-SWAP 3倍，保证金模式: cross
2025-08-05 11:30:41.232 [INFO] [exchanges.okx_exchange] OKX设置杠杆: DOGE-USDT-SWAP 3倍，保证金模式: cross
2025-08-05 11:30:41.232 [INFO] [exchanges.okx_exchange] OKX设置杠杆: SOL-USDT-SWAP 3倍，保证金模式: cross
2025-08-05 11:30:41.233 [INFO] [exchanges.okx_exchange] OKX设置杠杆: AVAX-USDT-SWAP 3倍，保证金模式: cross
2025-08-05 11:30:41.233 [INFO] [exchanges.okx_exchange] OKX设置杠杆: DOT-USDT-SWAP 3倍，保证金模式: cross
2025-08-05 11:30:41.233 [INFO] [exchanges.okx_exchange] OKX设置杠杆: SHIB-USDT-SWAP 3倍，保证金模式: cross
2025-08-05 11:30:41.233 [INFO] [exchanges.okx_exchange] OKX设置杠杆: BNB-USDT-SWAP 3倍，保证金模式: cross
2025-08-05 11:30:41.311 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-08-05 11:30:41.311 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-05 11:30:41.812 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-08-05 11:30:41.813 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-05 11:30:41.813 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-08-05 11:30:41.813 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-08-05 11:30:41.816 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-08-05 11:30:41.817 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-05 11:30:41.817 [ERROR] [exchanges.okx_exchange] OKX API错误详情:
2025-08-05 11:30:41.817 [ERROR] [exchanges.okx_exchange]   - 错误代码: 50011
2025-08-05 11:30:41.817 [ERROR] [exchanges.okx_exchange]   - 错误信息: Too Many Requests
2025-08-05 11:30:41.817 [ERROR] [exchanges.okx_exchange]   - 完整响应: {'msg': 'Too Many Requests', 'code': '50011'}
2025-08-05 11:30:41.817 [ERROR] [exchanges.okx_exchange]   - 请求端点: /api/v5/account/config
2025-08-05 11:30:41.818 [ERROR] [exchanges.okx_exchange]   - 请求方法: GET
2025-08-05 11:30:41.818 [ERROR] [exchanges.okx_exchange] OKX API调用失败: OKX API错误: Too Many Requests (代码: 50011)
2025-08-05 11:30:41.818 [ERROR] [exchanges.okx_exchange] 错误详情: ('OKX API错误: Too Many Requests (代码: 50011)',)
2025-08-05 11:30:41 [WARNING] [OKXExchange] 🚨 OKX API限速错误，2.0秒后重试 (尝试 1/4)
2025-08-05 11:30:41.819 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-08-05 11:30:41.819 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-05 11:30:41.820 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-08-05 11:30:41.820 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-05 11:30:41.858 [ERROR] [exchanges.okx_exchange] OKX API错误详情:
2025-08-05 11:30:41.859 [ERROR] [exchanges.okx_exchange]   - 错误代码: 50011
2025-08-05 11:30:41.859 [ERROR] [exchanges.okx_exchange]   - 错误信息: Too Many Requests
2025-08-05 11:30:41.859 [ERROR] [exchanges.okx_exchange]   - 完整响应: {'msg': 'Too Many Requests', 'code': '50011'}
2025-08-05 11:30:41.859 [ERROR] [exchanges.okx_exchange]   - 请求端点: /api/v5/account/config
2025-08-05 11:30:41.859 [ERROR] [exchanges.okx_exchange]   - 请求方法: GET
2025-08-05 11:30:41.859 [ERROR] [exchanges.okx_exchange] OKX API调用失败: OKX API错误: Too Many Requests (代码: 50011)
2025-08-05 11:30:41.859 [ERROR] [exchanges.okx_exchange] 错误详情: ('OKX API错误: Too Many Requests (代码: 50011)',)
2025-08-05 11:30:41 [WARNING] [OKXExchange] 🚨 OKX API限速错误，2.0秒后重试 (尝试 1/4)
2025-08-05 11:30:42.312 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-08-05 11:30:42.312 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-08-05 11:30:42.321 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-08-05 11:30:42.322 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-08-05 11:30:42.326 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-08-05 11:30:42.326 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-08-05 11:30:42.343 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-08-05 11:30:42.343 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-08-05 11:30:43.898 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-08-05 11:30:43.898 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-05 11:30:44.413 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-08-05 11:30:44.413 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-05 11:30:44.432 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-08-05 11:30:44.433 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-08-05 11:30:44.898 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-08-05 11:30:44.898 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-08-05 11:31:03.390 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: ADA-USDT -> 最大杠杆=50x
2025-08-05 11:31:03.845 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: BNB-USDT -> 最大杠杆=50x
2025-08-05 11:31:03.845 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: SOL-USDT -> 最大杠杆=50x
2025-08-05 11:31:03.847 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: DOT-USDT -> 最大杠杆=50x
2025-08-05 11:31:03.850 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: AVAX-USDT -> 最大杠杆=50x
2025-08-05 11:31:03.854 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: DOGE-USDT -> 最大杠杆=50x
2025-08-05 11:31:03.867 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: SHIB-USDT -> 最大杠杆=50x
2025-08-05 11:31:11.206 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: ADA-USDT -> 最大杠杆=50x
2025-08-05 11:31:11.717 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: SOL-USDT -> 最大杠杆=50x
2025-08-05 11:31:11.717 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: BNB-USDT -> 最大杠杆=50x
2025-08-05 11:31:11.730 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: DOGE-USDT -> 最大杠杆=50x
2025-08-05 11:31:11.731 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: DOT-USDT -> 最大杠杆=50x
2025-08-05 11:31:11.740 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: AVAX-USDT -> 最大杠杆=50x
2025-08-05 11:31:11.740 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: SHIB-USDT -> 最大杠杆=50x
