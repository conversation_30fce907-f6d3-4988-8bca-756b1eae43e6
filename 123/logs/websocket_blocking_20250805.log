2025-08-05 19:40:57,625 - BLOCKING - INFO - 🔍 WebSocket数据流阻塞追踪器已启动
2025-08-05 19:40:57,633 - BLOCKING - DEBUG - 📊 gate_spot 数据更新: symbol=BTC-USDT, 总消息=1, 频率=120.23/s
2025-08-05 19:40:57,633 - BLOCKING - DEBUG - 📊 okx_futures 数据更新: symbol=ETH-USDT, 总消息=1, 频率=117.64/s
2025-08-05 19:40:57,634 - BLOCKING - DEBUG - 📊 bybit_spot 数据更新: symbol=SOL-USDT, 总消息=1, 频率=116.99/s
2025-08-05 19:40:57,634 - BLOCKING - DEBUG - 📊 gate_spot 数据更新: symbol=BTC-USDT, 总消息=2, 频率=231.07/s
2025-08-05 19:40:57,634 - BLOCKING - DEBUG - 📊 gate_spot 数据更新: symbol=ETH-USDT, 总消息=3, 频率=344.27/s
2025-08-05 19:40:57,634 - BLOCKING - DEBUG - 📊 gate_spot 数据更新: symbol=SOL-USDT, 总消息=4, 频率=457.13/s
2025-08-05 19:40:57,634 - BLOCKING - DEBUG - 📊 gate_spot 数据更新: symbol=ADA-USDT, 总消息=5, 频率=569.71/s
2025-08-05 19:40:57,634 - BLOCKING - DEBUG - 📊 gate_spot 数据更新: symbol=DOT-USDT, 总消息=6, 频率=681.74/s
2025-08-05 19:40:57,634 - BLOCKING - DEBUG - 📊 okx_spot 数据更新: symbol=BTC-USDT, 总消息=1, 频率=113.30/s
2025-08-05 19:40:57,634 - BLOCKING - DEBUG - 📊 okx_spot 数据更新: symbol=ETH-USDT, 总消息=2, 频率=225.89/s
2025-08-05 19:40:57,634 - BLOCKING - DEBUG - 📊 okx_spot 数据更新: symbol=SOL-USDT, 总消息=3, 频率=337.88/s
2025-08-05 19:40:57,634 - BLOCKING - DEBUG - 📊 okx_spot 数据更新: symbol=ADA-USDT, 总消息=4, 频率=448.40/s
2025-08-05 19:40:57,634 - BLOCKING - DEBUG - 📊 okx_spot 数据更新: symbol=DOT-USDT, 总消息=5, 频率=555.11/s
2025-08-05 19:40:57,634 - BLOCKING - DEBUG - 📊 bybit_spot 数据更新: symbol=BTC-USDT, 总消息=2, 频率=220.68/s
2025-08-05 19:40:57,634 - BLOCKING - DEBUG - 📊 bybit_spot 数据更新: symbol=ETH-USDT, 总消息=3, 频率=329.49/s
2025-08-05 19:40:57,634 - BLOCKING - DEBUG - 📊 bybit_spot 数据更新: symbol=SOL-USDT, 总消息=4, 频率=437.75/s
2025-08-05 19:40:57,634 - BLOCKING - DEBUG - 📊 bybit_spot 数据更新: symbol=ADA-USDT, 总消息=5, 频率=545.66/s
2025-08-05 19:40:57,634 - BLOCKING - DEBUG - 📊 bybit_spot 数据更新: symbol=DOT-USDT, 总消息=6, 频率=653.10/s
2025-08-05 19:40:57,634 - BLOCKING - DEBUG - 📊 test_exchange_spot 数据更新: symbol=BTC-USDT, 总消息=1, 频率=108.24/s
2025-08-05 19:40:57,736 - BLOCKING - DEBUG - 📊 gate_spot 数据更新: symbol=TEST0-USDT, 总消息=7, 频率=63.14/s
2025-08-05 19:40:57,736 - BLOCKING - DEBUG - 📊 okx_spot 数据更新: symbol=TEST0-USDT, 总消息=6, 频率=53.95/s
2025-08-05 19:40:57,736 - BLOCKING - DEBUG - 📊 bybit_spot 数据更新: symbol=TEST0-USDT, 总消息=7, 频率=62.80/s
2025-08-05 19:40:57,737 - BLOCKING - DEBUG - 📊 gate_spot 数据更新: symbol=TEST1-USDT, 总消息=8, 频率=71.14/s
2025-08-05 19:40:57,738 - BLOCKING - DEBUG - 📊 okx_spot 数据更新: symbol=TEST1-USDT, 总消息=7, 频率=62.06/s
2025-08-05 19:40:57,738 - BLOCKING - DEBUG - 📊 bybit_spot 数据更新: symbol=TEST1-USDT, 总消息=8, 频率=70.80/s
2025-08-05 19:40:57,739 - BLOCKING - DEBUG - 📊 gate_spot 数据更新: symbol=TEST2-USDT, 总消息=9, 频率=79.17/s
2025-08-05 19:40:57,739 - BLOCKING - DEBUG - 📊 okx_spot 数据更新: symbol=TEST2-USDT, 总消息=8, 频率=70.20/s
2025-08-05 19:40:57,739 - BLOCKING - DEBUG - 📊 bybit_spot 数据更新: symbol=TEST2-USDT, 总消息=9, 频率=78.78/s
2025-08-05 19:40:57,740 - BLOCKING - DEBUG - 📊 gate_spot 数据更新: symbol=TEST3-USDT, 总消息=10, 频率=87.03/s
2025-08-05 19:40:57,740 - BLOCKING - DEBUG - 📊 okx_spot 数据更新: symbol=TEST3-USDT, 总消息=9, 频率=78.13/s
2025-08-05 19:40:57,741 - BLOCKING - DEBUG - 📊 bybit_spot 数据更新: symbol=TEST3-USDT, 总消息=10, 频率=86.50/s
2025-08-05 19:40:57,741 - BLOCKING - DEBUG - 📊 gate_spot 数据更新: symbol=TEST4-USDT, 总消息=11, 频率=94.73/s
2025-08-05 19:40:57,741 - BLOCKING - DEBUG - 📊 okx_spot 数据更新: symbol=TEST4-USDT, 总消息=10, 频率=85.93/s
2025-08-05 19:40:57,742 - BLOCKING - DEBUG - 📊 bybit_spot 数据更新: symbol=TEST4-USDT, 总消息=11, 频率=94.13/s
2025-08-05 19:40:57,742 - BLOCKING - DEBUG - 📊 gate_spot 数据更新: symbol=TEST5-USDT, 总消息=12, 频率=102.29/s
2025-08-05 19:40:57,743 - BLOCKING - DEBUG - 📊 okx_spot 数据更新: symbol=TEST5-USDT, 总消息=11, 频率=93.58/s
2025-08-05 19:40:57,743 - BLOCKING - DEBUG - 📊 bybit_spot 数据更新: symbol=TEST5-USDT, 总消息=12, 频率=101.63/s
2025-08-05 19:40:57,743 - BLOCKING - DEBUG - 📊 gate_spot 数据更新: symbol=TEST6-USDT, 总消息=13, 频率=109.72/s
2025-08-05 19:40:57,744 - BLOCKING - DEBUG - 📊 okx_spot 数据更新: symbol=TEST6-USDT, 总消息=12, 频率=101.07/s
2025-08-05 19:40:57,744 - BLOCKING - DEBUG - 📊 bybit_spot 数据更新: symbol=TEST6-USDT, 总消息=13, 频率=109.03/s
2025-08-05 19:40:57,745 - BLOCKING - DEBUG - 📊 gate_spot 数据更新: symbol=TEST7-USDT, 总消息=14, 频率=117.02/s
2025-08-05 19:40:57,745 - BLOCKING - DEBUG - 📊 okx_spot 数据更新: symbol=TEST7-USDT, 总消息=13, 频率=108.43/s
2025-08-05 19:40:57,745 - BLOCKING - DEBUG - 📊 bybit_spot 数据更新: symbol=TEST7-USDT, 总消息=14, 频率=116.29/s
2025-08-05 19:40:57,746 - BLOCKING - DEBUG - 📊 gate_spot 数据更新: symbol=TEST8-USDT, 总消息=15, 频率=124.16/s
2025-08-05 19:40:57,746 - BLOCKING - DEBUG - 📊 okx_spot 数据更新: symbol=TEST8-USDT, 总消息=14, 频率=115.63/s
2025-08-05 19:40:57,747 - BLOCKING - DEBUG - 📊 bybit_spot 数据更新: symbol=TEST8-USDT, 总消息=15, 频率=123.41/s
2025-08-05 19:40:57,747 - BLOCKING - DEBUG - 📊 gate_spot 数据更新: symbol=TEST9-USDT, 总消息=16, 频率=131.15/s
2025-08-05 19:40:57,747 - BLOCKING - DEBUG - 📊 okx_spot 数据更新: symbol=TEST9-USDT, 总消息=15, 频率=122.71/s
2025-08-05 19:40:57,748 - BLOCKING - DEBUG - 📊 bybit_spot 数据更新: symbol=TEST9-USDT, 总消息=16, 频率=130.40/s
2025-08-05 19:40:57,748 - BLOCKING - DEBUG - 📊 gate_spot 数据更新: symbol=TEST10-USDT, 总消息=17, 频率=138.03/s
2025-08-05 19:40:57,748 - BLOCKING - DEBUG - 📊 okx_spot 数据更新: symbol=TEST10-USDT, 总消息=16, 频率=129.66/s
2025-08-05 19:40:57,749 - BLOCKING - DEBUG - 📊 bybit_spot 数据更新: symbol=TEST10-USDT, 总消息=17, 频率=137.26/s
2025-08-05 19:40:57,749 - BLOCKING - DEBUG - 📊 gate_spot 数据更新: symbol=TEST11-USDT, 总消息=18, 频率=144.78/s
2025-08-05 19:40:57,750 - BLOCKING - DEBUG - 📊 okx_spot 数据更新: symbol=TEST11-USDT, 总消息=17, 频率=136.48/s
2025-08-05 19:40:57,750 - BLOCKING - DEBUG - 📊 bybit_spot 数据更新: symbol=TEST11-USDT, 总消息=18, 频率=143.99/s
2025-08-05 19:40:57,750 - BLOCKING - DEBUG - 📊 gate_spot 数据更新: symbol=TEST12-USDT, 总消息=19, 频率=151.39/s
2025-08-05 19:40:57,751 - BLOCKING - DEBUG - 📊 okx_spot 数据更新: symbol=TEST12-USDT, 总消息=18, 频率=143.16/s
2025-08-05 19:40:57,751 - BLOCKING - DEBUG - 📊 bybit_spot 数据更新: symbol=TEST12-USDT, 总消息=19, 频率=150.61/s
2025-08-05 19:40:57,752 - BLOCKING - DEBUG - 📊 gate_spot 数据更新: symbol=TEST13-USDT, 总消息=20, 频率=157.92/s
2025-08-05 19:40:57,752 - BLOCKING - DEBUG - 📊 okx_spot 数据更新: symbol=TEST13-USDT, 总消息=19, 频率=149.72/s
2025-08-05 19:40:57,752 - BLOCKING - DEBUG - 📊 bybit_spot 数据更新: symbol=TEST13-USDT, 总消息=20, 频率=157.10/s
2025-08-05 19:40:57,753 - BLOCKING - DEBUG - 📊 gate_spot 数据更新: symbol=TEST14-USDT, 总消息=21, 频率=164.30/s
2025-08-05 19:40:57,753 - BLOCKING - DEBUG - 📊 okx_spot 数据更新: symbol=TEST14-USDT, 总消息=20, 频率=156.18/s
2025-08-05 19:40:57,753 - BLOCKING - DEBUG - 📊 bybit_spot 数据更新: symbol=TEST14-USDT, 总消息=21, 频率=163.49/s
2025-08-05 19:40:57,754 - BLOCKING - DEBUG - 📊 gate_spot 数据更新: symbol=TEST15-USDT, 总消息=22, 频率=170.59/s
2025-08-05 19:40:57,754 - BLOCKING - DEBUG - 📊 okx_spot 数据更新: symbol=TEST15-USDT, 总消息=21, 频率=162.52/s
2025-08-05 19:40:57,755 - BLOCKING - DEBUG - 📊 bybit_spot 数据更新: symbol=TEST15-USDT, 总消息=22, 频率=169.77/s
2025-08-05 19:40:57,755 - BLOCKING - DEBUG - 📊 gate_spot 数据更新: symbol=TEST16-USDT, 总消息=23, 频率=176.76/s
2025-08-05 19:40:57,755 - BLOCKING - DEBUG - 📊 okx_spot 数据更新: symbol=TEST16-USDT, 总消息=22, 频率=168.75/s
2025-08-05 19:40:57,756 - BLOCKING - DEBUG - 📊 bybit_spot 数据更新: symbol=TEST16-USDT, 总消息=23, 频率=175.89/s
2025-08-05 19:40:57,756 - BLOCKING - DEBUG - 📊 gate_spot 数据更新: symbol=TEST17-USDT, 总消息=24, 频率=182.84/s
2025-08-05 19:40:57,757 - BLOCKING - DEBUG - 📊 okx_spot 数据更新: symbol=TEST17-USDT, 总消息=23, 频率=174.89/s
2025-08-05 19:40:57,757 - BLOCKING - DEBUG - 📊 bybit_spot 数据更新: symbol=TEST17-USDT, 总消息=24, 频率=181.95/s
2025-08-05 19:40:57,757 - BLOCKING - DEBUG - 📊 gate_spot 数据更新: symbol=TEST18-USDT, 总消息=25, 频率=188.80/s
2025-08-05 19:40:57,758 - BLOCKING - DEBUG - 📊 okx_spot 数据更新: symbol=TEST18-USDT, 总消息=24, 频率=180.93/s
2025-08-05 19:40:57,758 - BLOCKING - DEBUG - 📊 bybit_spot 数据更新: symbol=TEST18-USDT, 总消息=25, 频率=187.90/s
2025-08-05 19:40:57,759 - BLOCKING - DEBUG - 📊 gate_spot 数据更新: symbol=TEST19-USDT, 总消息=26, 频率=194.68/s
2025-08-05 19:40:57,759 - BLOCKING - DEBUG - 📊 okx_spot 数据更新: symbol=TEST19-USDT, 总消息=25, 频率=186.86/s
2025-08-05 19:40:57,759 - BLOCKING - DEBUG - 📊 bybit_spot 数据更新: symbol=TEST19-USDT, 总消息=26, 频率=193.75/s
2025-08-05 19:40:57,760 - BLOCKING - DEBUG - 📊 gate_spot 数据更新: symbol=TEST20-USDT, 总消息=27, 频率=200.44/s
2025-08-05 19:40:57,760 - BLOCKING - DEBUG - 📊 okx_spot 数据更新: symbol=TEST20-USDT, 总消息=26, 频率=192.71/s
2025-08-05 19:40:57,760 - BLOCKING - DEBUG - 📊 bybit_spot 数据更新: symbol=TEST20-USDT, 总消息=27, 频率=199.29/s
2025-08-05 19:40:57,761 - BLOCKING - DEBUG - 📊 gate_spot 数据更新: symbol=TEST21-USDT, 总消息=28, 频率=205.92/s
2025-08-05 19:40:57,761 - BLOCKING - DEBUG - 📊 okx_spot 数据更新: symbol=TEST21-USDT, 总消息=27, 频率=198.02/s
2025-08-05 19:40:57,762 - BLOCKING - DEBUG - 📊 bybit_spot 数据更新: symbol=TEST21-USDT, 总消息=28, 频率=204.67/s
2025-08-05 19:40:57,762 - BLOCKING - DEBUG - 📊 gate_spot 数据更新: symbol=TEST22-USDT, 总消息=29, 频率=211.20/s
2025-08-05 19:40:57,763 - BLOCKING - DEBUG - 📊 okx_spot 数据更新: symbol=TEST22-USDT, 总消息=28, 频率=203.25/s
2025-08-05 19:40:57,763 - BLOCKING - DEBUG - 📊 bybit_spot 数据更新: symbol=TEST22-USDT, 总消息=29, 频率=210.10/s
2025-08-05 19:40:57,763 - BLOCKING - DEBUG - 📊 gate_spot 数据更新: symbol=TEST23-USDT, 总消息=30, 频率=216.68/s
2025-08-05 19:40:57,764 - BLOCKING - DEBUG - 📊 okx_spot 数据更新: symbol=TEST23-USDT, 总消息=29, 频率=208.73/s
2025-08-05 19:40:57,764 - BLOCKING - DEBUG - 📊 bybit_spot 数据更新: symbol=TEST23-USDT, 总消息=30, 频率=215.51/s
2025-08-05 19:40:57,765 - BLOCKING - DEBUG - 📊 gate_spot 数据更新: symbol=TEST24-USDT, 总消息=31, 频率=222.07/s
2025-08-05 19:40:57,765 - BLOCKING - DEBUG - 📊 okx_spot 数据更新: symbol=TEST24-USDT, 总消息=30, 频率=214.10/s
2025-08-05 19:40:57,765 - BLOCKING - DEBUG - 📊 bybit_spot 数据更新: symbol=TEST24-USDT, 总消息=31, 频率=220.82/s
2025-08-05 19:40:57,766 - BLOCKING - DEBUG - 📊 gate_spot 数据更新: symbol=TEST25-USDT, 总消息=32, 频率=227.30/s
2025-08-05 19:40:57,766 - BLOCKING - DEBUG - 📊 okx_spot 数据更新: symbol=TEST25-USDT, 总消息=31, 频率=219.42/s
2025-08-05 19:40:57,767 - BLOCKING - DEBUG - 📊 bybit_spot 数据更新: symbol=TEST25-USDT, 总消息=32, 频率=226.08/s
2025-08-05 19:40:57,767 - BLOCKING - DEBUG - 📊 gate_spot 数据更新: symbol=TEST26-USDT, 总消息=33, 频率=232.46/s
2025-08-05 19:40:57,767 - BLOCKING - DEBUG - 📊 okx_spot 数据更新: symbol=TEST26-USDT, 总消息=32, 频率=224.66/s
2025-08-05 19:40:57,768 - BLOCKING - DEBUG - 📊 bybit_spot 数据更新: symbol=TEST26-USDT, 总消息=33, 频率=231.25/s
2025-08-05 19:40:57,768 - BLOCKING - DEBUG - 📊 gate_spot 数据更新: symbol=TEST27-USDT, 总消息=34, 频率=237.52/s
2025-08-05 19:40:57,769 - BLOCKING - DEBUG - 📊 okx_spot 数据更新: symbol=TEST27-USDT, 总消息=33, 频率=229.81/s
2025-08-05 19:40:57,769 - BLOCKING - DEBUG - 📊 bybit_spot 数据更新: symbol=TEST27-USDT, 总消息=34, 频率=236.31/s
2025-08-05 19:40:57,769 - BLOCKING - DEBUG - 📊 gate_spot 数据更新: symbol=TEST28-USDT, 总消息=35, 频率=242.54/s
2025-08-05 19:40:57,770 - BLOCKING - DEBUG - 📊 okx_spot 数据更新: symbol=TEST28-USDT, 总消息=34, 频率=234.85/s
2025-08-05 19:40:57,770 - BLOCKING - DEBUG - 📊 bybit_spot 数据更新: symbol=TEST28-USDT, 总消息=35, 频率=241.32/s
2025-08-05 19:40:57,770 - BLOCKING - DEBUG - 📊 gate_spot 数据更新: symbol=TEST29-USDT, 总消息=36, 频率=247.48/s
2025-08-05 19:40:57,771 - BLOCKING - DEBUG - 📊 okx_spot 数据更新: symbol=TEST29-USDT, 总消息=35, 频率=239.84/s
2025-08-05 19:40:57,771 - BLOCKING - DEBUG - 📊 bybit_spot 数据更新: symbol=TEST29-USDT, 总消息=36, 频率=246.25/s
2025-08-05 19:40:57,772 - BLOCKING - DEBUG - 📊 gate_spot 数据更新: symbol=TEST30-USDT, 总消息=37, 频率=252.36/s
2025-08-05 19:40:57,772 - BLOCKING - DEBUG - 📊 okx_spot 数据更新: symbol=TEST30-USDT, 总消息=36, 频率=244.71/s
2025-08-05 19:40:57,772 - BLOCKING - DEBUG - 📊 bybit_spot 数据更新: symbol=TEST30-USDT, 总消息=37, 频率=251.11/s
2025-08-05 19:40:57,773 - BLOCKING - DEBUG - 📊 gate_spot 数据更新: symbol=TEST31-USDT, 总消息=38, 频率=257.13/s
2025-08-05 19:40:57,773 - BLOCKING - DEBUG - 📊 okx_spot 数据更新: symbol=TEST31-USDT, 总消息=37, 频率=249.55/s
2025-08-05 19:40:57,773 - BLOCKING - DEBUG - 📊 bybit_spot 数据更新: symbol=TEST31-USDT, 总消息=38, 频率=255.89/s
2025-08-05 19:40:57,774 - BLOCKING - DEBUG - 📊 gate_spot 数据更新: symbol=TEST32-USDT, 总消息=39, 频率=261.86/s
2025-08-05 19:40:57,774 - BLOCKING - DEBUG - 📊 okx_spot 数据更新: symbol=TEST32-USDT, 总消息=38, 频率=254.30/s
2025-08-05 19:40:57,775 - BLOCKING - DEBUG - 📊 bybit_spot 数据更新: symbol=TEST32-USDT, 总消息=39, 频率=260.59/s
2025-08-05 19:40:57,775 - BLOCKING - DEBUG - 📊 gate_spot 数据更新: symbol=TEST33-USDT, 总消息=40, 频率=266.45/s
2025-08-05 19:40:57,776 - BLOCKING - DEBUG - 📊 okx_spot 数据更新: symbol=TEST33-USDT, 总消息=39, 频率=258.98/s
2025-08-05 19:40:57,776 - BLOCKING - DEBUG - 📊 bybit_spot 数据更新: symbol=TEST33-USDT, 总消息=40, 频率=265.16/s
2025-08-05 19:40:57,776 - BLOCKING - DEBUG - 📊 gate_spot 数据更新: symbol=TEST34-USDT, 总消息=41, 频率=271.00/s
2025-08-05 19:40:57,777 - BLOCKING - DEBUG - 📊 okx_spot 数据更新: symbol=TEST34-USDT, 总消息=40, 频率=263.55/s
2025-08-05 19:40:57,777 - BLOCKING - DEBUG - 📊 bybit_spot 数据更新: symbol=TEST34-USDT, 总消息=41, 频率=269.69/s
2025-08-05 19:40:57,777 - BLOCKING - DEBUG - 📊 gate_spot 数据更新: symbol=TEST35-USDT, 总消息=42, 频率=275.52/s
2025-08-05 19:40:57,778 - BLOCKING - DEBUG - 📊 okx_spot 数据更新: symbol=TEST35-USDT, 总消息=41, 频率=268.10/s
2025-08-05 19:40:57,778 - BLOCKING - DEBUG - 📊 bybit_spot 数据更新: symbol=TEST35-USDT, 总消息=42, 频率=274.14/s
2025-08-05 19:40:57,779 - BLOCKING - DEBUG - 📊 gate_spot 数据更新: symbol=TEST36-USDT, 总消息=43, 频率=279.95/s
2025-08-05 19:40:57,779 - BLOCKING - DEBUG - 📊 okx_spot 数据更新: symbol=TEST36-USDT, 总消息=42, 频率=272.55/s
2025-08-05 19:40:57,779 - BLOCKING - DEBUG - 📊 bybit_spot 数据更新: symbol=TEST36-USDT, 总消息=43, 频率=278.54/s
2025-08-05 19:40:57,780 - BLOCKING - DEBUG - 📊 gate_spot 数据更新: symbol=TEST37-USDT, 总消息=44, 频率=284.30/s
2025-08-05 19:40:57,780 - BLOCKING - DEBUG - 📊 okx_spot 数据更新: symbol=TEST37-USDT, 总消息=43, 频率=276.96/s
2025-08-05 19:40:57,781 - BLOCKING - DEBUG - 📊 bybit_spot 数据更新: symbol=TEST37-USDT, 总消息=44, 频率=282.85/s
2025-08-05 19:40:57,781 - BLOCKING - DEBUG - 📊 gate_spot 数据更新: symbol=TEST38-USDT, 总消息=45, 频率=288.59/s
2025-08-05 19:40:57,781 - BLOCKING - DEBUG - 📊 okx_spot 数据更新: symbol=TEST38-USDT, 总消息=44, 频率=281.27/s
2025-08-05 19:40:57,782 - BLOCKING - DEBUG - 📊 bybit_spot 数据更新: symbol=TEST38-USDT, 总消息=45, 频率=287.08/s
2025-08-05 19:40:57,782 - BLOCKING - DEBUG - 📊 gate_spot 数据更新: symbol=TEST39-USDT, 总消息=46, 频率=292.79/s
2025-08-05 19:40:57,783 - BLOCKING - DEBUG - 📊 okx_spot 数据更新: symbol=TEST39-USDT, 总消息=45, 频率=285.55/s
2025-08-05 19:40:57,783 - BLOCKING - DEBUG - 📊 bybit_spot 数据更新: symbol=TEST39-USDT, 总消息=46, 频率=291.32/s
2025-08-05 19:40:57,783 - BLOCKING - DEBUG - 📊 gate_spot 数据更新: symbol=TEST40-USDT, 总消息=47, 频率=296.94/s
2025-08-05 19:40:57,784 - BLOCKING - DEBUG - 📊 okx_spot 数据更新: symbol=TEST40-USDT, 总消息=46, 频率=289.74/s
2025-08-05 19:40:57,784 - BLOCKING - DEBUG - 📊 bybit_spot 数据更新: symbol=TEST40-USDT, 总消息=47, 频率=295.48/s
2025-08-05 19:40:57,784 - BLOCKING - DEBUG - 📊 gate_spot 数据更新: symbol=TEST41-USDT, 总消息=48, 频率=301.00/s
2025-08-05 19:40:57,785 - BLOCKING - DEBUG - 📊 okx_spot 数据更新: symbol=TEST41-USDT, 总消息=47, 频率=293.90/s
2025-08-05 19:40:57,785 - BLOCKING - DEBUG - 📊 bybit_spot 数据更新: symbol=TEST41-USDT, 总消息=48, 频率=299.55/s
2025-08-05 19:40:57,786 - BLOCKING - DEBUG - 📊 gate_spot 数据更新: symbol=TEST42-USDT, 总消息=49, 频率=305.05/s
2025-08-05 19:40:57,786 - BLOCKING - DEBUG - 📊 okx_spot 数据更新: symbol=TEST42-USDT, 总消息=48, 频率=297.89/s
2025-08-05 19:40:57,786 - BLOCKING - DEBUG - 📊 bybit_spot 数据更新: symbol=TEST42-USDT, 总消息=49, 频率=303.59/s
2025-08-05 19:40:57,787 - BLOCKING - DEBUG - 📊 gate_spot 数据更新: symbol=TEST43-USDT, 总消息=50, 频率=309.04/s
2025-08-05 19:40:57,787 - BLOCKING - DEBUG - 📊 okx_spot 数据更新: symbol=TEST43-USDT, 总消息=49, 频率=301.93/s
2025-08-05 19:40:57,788 - BLOCKING - DEBUG - 📊 bybit_spot 数据更新: symbol=TEST43-USDT, 总消息=50, 频率=307.57/s
2025-08-05 19:40:57,788 - BLOCKING - DEBUG - 📊 gate_spot 数据更新: symbol=TEST44-USDT, 总消息=51, 频率=312.98/s
2025-08-05 19:40:57,788 - BLOCKING - DEBUG - 📊 okx_spot 数据更新: symbol=TEST44-USDT, 总消息=50, 频率=305.88/s
2025-08-05 19:40:57,789 - BLOCKING - DEBUG - 📊 bybit_spot 数据更新: symbol=TEST44-USDT, 总消息=51, 频率=311.49/s
2025-08-05 19:40:57,789 - BLOCKING - DEBUG - 📊 gate_spot 数据更新: symbol=TEST45-USDT, 总消息=52, 频率=316.83/s
2025-08-05 19:40:57,790 - BLOCKING - DEBUG - 📊 okx_spot 数据更新: symbol=TEST45-USDT, 总消息=51, 频率=309.78/s
2025-08-05 19:40:57,790 - BLOCKING - DEBUG - 📊 bybit_spot 数据更新: symbol=TEST45-USDT, 总消息=52, 频率=315.37/s
2025-08-05 19:40:57,790 - BLOCKING - DEBUG - 📊 gate_spot 数据更新: symbol=TEST46-USDT, 总消息=53, 频率=320.68/s
2025-08-05 19:40:57,791 - BLOCKING - DEBUG - 📊 okx_spot 数据更新: symbol=TEST46-USDT, 总消息=52, 频率=313.62/s
2025-08-05 19:40:57,791 - BLOCKING - DEBUG - 📊 bybit_spot 数据更新: symbol=TEST46-USDT, 总消息=53, 频率=319.26/s
2025-08-05 19:40:57,791 - BLOCKING - DEBUG - 📊 gate_spot 数据更新: symbol=TEST47-USDT, 总消息=54, 频率=324.43/s
2025-08-05 19:40:57,792 - BLOCKING - DEBUG - 📊 okx_spot 数据更新: symbol=TEST47-USDT, 总消息=53, 频率=317.42/s
2025-08-05 19:40:57,792 - BLOCKING - DEBUG - 📊 bybit_spot 数据更新: symbol=TEST47-USDT, 总消息=54, 频率=322.97/s
2025-08-05 19:40:57,793 - BLOCKING - DEBUG - 📊 gate_spot 数据更新: symbol=TEST48-USDT, 总消息=55, 频率=328.12/s
2025-08-05 19:40:57,793 - BLOCKING - DEBUG - 📊 okx_spot 数据更新: symbol=TEST48-USDT, 总消息=54, 频率=321.09/s
2025-08-05 19:40:57,793 - BLOCKING - DEBUG - 📊 bybit_spot 数据更新: symbol=TEST48-USDT, 总消息=55, 频率=326.73/s
2025-08-05 19:40:57,794 - BLOCKING - DEBUG - 📊 gate_spot 数据更新: symbol=TEST49-USDT, 总消息=56, 频率=331.75/s
2025-08-05 19:40:57,794 - BLOCKING - DEBUG - 📊 okx_spot 数据更新: symbol=TEST49-USDT, 总消息=55, 频率=324.79/s
2025-08-05 19:40:57,795 - BLOCKING - DEBUG - 📊 bybit_spot 数据更新: symbol=TEST49-USDT, 总消息=56, 频率=330.35/s
2025-08-05 19:40:57,796 - BLOCKING - INFO - 🔗 连接事件: test_gate_spot - data_staleness_detected - {'silent_duration_seconds': 85, 'last_update_time': 1754415572796, 'staleness_threshold_ms': 30000}
2025-08-05 19:40:57,796 - BLOCKING - INFO - 🔗 连接事件: gate_spot - data_staleness_detected - {'silent_duration_seconds': 80.862, 'last_update_time': 1754412617489, 'staleness_threshold_ms': 1000}
2025-08-05 19:40:57,796 - BLOCKING - INFO - 🔗 连接事件: okx_spot - data_staleness_detected - {'silent_duration_seconds': 86.237, 'last_update_time': 1754412611601, 'staleness_threshold_ms': 1000}
2025-08-05 19:40:57,796 - BLOCKING - DEBUG - 📊 performance_test_spot 数据更新: symbol=TEST0, 总消息=1, 频率=5.83/s
2025-08-05 19:40:57,797 - BLOCKING - DEBUG - 📊 performance_test_spot 数据更新: symbol=TEST1, 总消息=2, 频率=11.66/s
2025-08-05 19:40:57,797 - BLOCKING - DEBUG - 📊 performance_test_spot 数据更新: symbol=TEST2, 总消息=3, 频率=17.48/s
2025-08-05 19:40:57,797 - BLOCKING - DEBUG - 📊 performance_test_spot 数据更新: symbol=TEST3, 总消息=4, 频率=23.30/s
2025-08-05 19:40:57,797 - BLOCKING - DEBUG - 📊 performance_test_spot 数据更新: symbol=TEST4, 总消息=5, 频率=29.12/s
2025-08-05 19:40:57,797 - BLOCKING - DEBUG - 📊 performance_test_spot 数据更新: symbol=TEST5, 总消息=6, 频率=34.94/s
2025-08-05 19:40:57,797 - BLOCKING - DEBUG - 📊 performance_test_spot 数据更新: symbol=TEST6, 总消息=7, 频率=40.74/s
2025-08-05 19:40:57,797 - BLOCKING - DEBUG - 📊 performance_test_spot 数据更新: symbol=TEST7, 总消息=8, 频率=46.51/s
2025-08-05 19:40:57,797 - BLOCKING - DEBUG - 📊 performance_test_spot 数据更新: symbol=TEST8, 总消息=9, 频率=52.30/s
2025-08-05 19:40:57,797 - BLOCKING - DEBUG - 📊 performance_test_spot 数据更新: symbol=TEST9, 总消息=10, 频率=58.10/s
2025-08-05 19:40:57,797 - BLOCKING - DEBUG - 📊 performance_test_spot 数据更新: symbol=TEST10, 总消息=11, 频率=63.90/s
2025-08-05 19:40:57,797 - BLOCKING - DEBUG - 📊 performance_test_spot 数据更新: symbol=TEST11, 总消息=12, 频率=69.69/s
2025-08-05 19:40:57,797 - BLOCKING - DEBUG - 📊 performance_test_spot 数据更新: symbol=TEST12, 总消息=13, 频率=75.48/s
2025-08-05 19:40:57,797 - BLOCKING - DEBUG - 📊 performance_test_spot 数据更新: symbol=TEST13, 总消息=14, 频率=81.27/s
2025-08-05 19:40:57,797 - BLOCKING - DEBUG - 📊 performance_test_spot 数据更新: symbol=TEST14, 总消息=15, 频率=87.05/s
2025-08-05 19:40:57,797 - BLOCKING - DEBUG - 📊 performance_test_spot 数据更新: symbol=TEST15, 总消息=16, 频率=92.84/s
2025-08-05 19:40:57,797 - BLOCKING - DEBUG - 📊 performance_test_spot 数据更新: symbol=TEST16, 总消息=17, 频率=98.62/s
2025-08-05 19:40:57,797 - BLOCKING - DEBUG - 📊 performance_test_spot 数据更新: symbol=TEST17, 总消息=18, 频率=104.40/s
2025-08-05 19:40:57,797 - BLOCKING - DEBUG - 📊 performance_test_spot 数据更新: symbol=TEST18, 总消息=19, 频率=110.18/s
2025-08-05 19:40:57,797 - BLOCKING - DEBUG - 📊 performance_test_spot 数据更新: symbol=TEST19, 总消息=20, 频率=115.95/s
2025-08-05 19:40:57,798 - BLOCKING - DEBUG - 📊 performance_test_spot 数据更新: symbol=TEST20, 总消息=21, 频率=121.69/s
2025-08-05 19:40:57,798 - BLOCKING - DEBUG - 📊 performance_test_spot 数据更新: symbol=TEST21, 总消息=22, 频率=127.43/s
2025-08-05 19:40:57,798 - BLOCKING - DEBUG - 📊 performance_test_spot 数据更新: symbol=TEST22, 总消息=23, 频率=133.17/s
2025-08-05 19:40:57,798 - BLOCKING - DEBUG - 📊 performance_test_spot 数据更新: symbol=TEST23, 总消息=24, 频率=138.92/s
2025-08-05 19:40:57,798 - BLOCKING - DEBUG - 📊 performance_test_spot 数据更新: symbol=TEST24, 总消息=25, 频率=144.64/s
2025-08-05 19:40:57,798 - BLOCKING - DEBUG - 📊 performance_test_spot 数据更新: symbol=TEST25, 总消息=26, 频率=150.38/s
2025-08-05 19:40:57,798 - BLOCKING - DEBUG - 📊 performance_test_spot 数据更新: symbol=TEST26, 总消息=27, 频率=156.13/s
2025-08-05 19:40:57,798 - BLOCKING - DEBUG - 📊 performance_test_spot 数据更新: symbol=TEST27, 总消息=28, 频率=161.88/s
2025-08-05 19:40:57,798 - BLOCKING - DEBUG - 📊 performance_test_spot 数据更新: symbol=TEST28, 总消息=29, 频率=167.38/s
2025-08-05 19:40:57,798 - BLOCKING - DEBUG - 📊 performance_test_spot 数据更新: symbol=TEST29, 总消息=30, 频率=173.10/s
2025-08-05 19:40:57,798 - BLOCKING - DEBUG - 📊 performance_test_spot 数据更新: symbol=TEST30, 总消息=31, 频率=178.83/s
2025-08-05 19:40:57,798 - BLOCKING - DEBUG - 📊 performance_test_spot 数据更新: symbol=TEST31, 总消息=32, 频率=184.56/s
2025-08-05 19:40:57,798 - BLOCKING - DEBUG - 📊 performance_test_spot 数据更新: symbol=TEST32, 总消息=33, 频率=190.28/s
2025-08-05 19:40:57,798 - BLOCKING - DEBUG - 📊 performance_test_spot 数据更新: symbol=TEST33, 总消息=34, 频率=196.01/s
2025-08-05 19:40:57,798 - BLOCKING - DEBUG - 📊 performance_test_spot 数据更新: symbol=TEST34, 总消息=35, 频率=201.73/s
2025-08-05 19:40:57,799 - BLOCKING - DEBUG - 📊 performance_test_spot 数据更新: symbol=TEST35, 总消息=36, 频率=207.45/s
2025-08-05 19:40:57,799 - BLOCKING - DEBUG - 📊 performance_test_spot 数据更新: symbol=TEST36, 总消息=37, 频率=213.16/s
2025-08-05 19:40:57,799 - BLOCKING - DEBUG - 📊 performance_test_spot 数据更新: symbol=TEST37, 总消息=38, 频率=218.87/s
2025-08-05 19:40:57,799 - BLOCKING - DEBUG - 📊 performance_test_spot 数据更新: symbol=TEST38, 总消息=39, 频率=224.58/s
2025-08-05 19:40:57,799 - BLOCKING - DEBUG - 📊 performance_test_spot 数据更新: symbol=TEST39, 总消息=40, 频率=230.27/s
2025-08-05 19:40:57,799 - BLOCKING - DEBUG - 📊 performance_test_spot 数据更新: symbol=TEST40, 总消息=41, 频率=235.98/s
2025-08-05 19:40:57,799 - BLOCKING - DEBUG - 📊 performance_test_spot 数据更新: symbol=TEST41, 总消息=42, 频率=241.69/s
2025-08-05 19:40:57,799 - BLOCKING - DEBUG - 📊 performance_test_spot 数据更新: symbol=TEST42, 总消息=43, 频率=247.40/s
2025-08-05 19:40:57,799 - BLOCKING - DEBUG - 📊 performance_test_spot 数据更新: symbol=TEST43, 总消息=44, 频率=253.09/s
2025-08-05 19:40:57,799 - BLOCKING - DEBUG - 📊 performance_test_spot 数据更新: symbol=TEST44, 总消息=45, 频率=258.78/s
2025-08-05 19:40:57,799 - BLOCKING - DEBUG - 📊 performance_test_spot 数据更新: symbol=TEST45, 总消息=46, 频率=264.47/s
2025-08-05 19:40:57,799 - BLOCKING - DEBUG - 📊 performance_test_spot 数据更新: symbol=TEST46, 总消息=47, 频率=270.17/s
2025-08-05 19:40:57,799 - BLOCKING - DEBUG - 📊 performance_test_spot 数据更新: symbol=TEST47, 总消息=48, 频率=275.81/s
2025-08-05 19:40:57,799 - BLOCKING - DEBUG - 📊 performance_test_spot 数据更新: symbol=TEST48, 总消息=49, 频率=281.49/s
2025-08-05 19:40:57,799 - BLOCKING - DEBUG - 📊 performance_test_spot 数据更新: symbol=TEST49, 总消息=50, 频率=287.18/s
2025-08-05 19:40:57,799 - BLOCKING - DEBUG - 📊 performance_test_spot 数据更新: symbol=TEST50, 总消息=51, 频率=292.86/s
2025-08-05 19:40:57,799 - BLOCKING - DEBUG - 📊 performance_test_spot 数据更新: symbol=TEST51, 总消息=52, 频率=298.53/s
2025-08-05 19:40:57,799 - BLOCKING - DEBUG - 📊 performance_test_spot 数据更新: symbol=TEST52, 总消息=53, 频率=304.20/s
2025-08-05 19:40:57,799 - BLOCKING - DEBUG - 📊 performance_test_spot 数据更新: symbol=TEST53, 总消息=54, 频率=309.87/s
2025-08-05 19:40:57,799 - BLOCKING - DEBUG - 📊 performance_test_spot 数据更新: symbol=TEST54, 总消息=55, 频率=315.54/s
2025-08-05 19:40:57,799 - BLOCKING - DEBUG - 📊 performance_test_spot 数据更新: symbol=TEST55, 总消息=56, 频率=321.19/s
2025-08-05 19:40:57,799 - BLOCKING - DEBUG - 📊 performance_test_spot 数据更新: symbol=TEST56, 总消息=57, 频率=326.84/s
2025-08-05 19:40:57,799 - BLOCKING - DEBUG - 📊 performance_test_spot 数据更新: symbol=TEST57, 总消息=58, 频率=332.50/s
2025-08-05 19:40:57,799 - BLOCKING - DEBUG - 📊 performance_test_spot 数据更新: symbol=TEST58, 总消息=59, 频率=338.16/s
2025-08-05 19:40:57,800 - BLOCKING - DEBUG - 📊 performance_test_spot 数据更新: symbol=TEST59, 总消息=60, 频率=343.81/s
2025-08-05 19:40:57,800 - BLOCKING - DEBUG - 📊 performance_test_spot 数据更新: symbol=TEST60, 总消息=61, 频率=349.46/s
2025-08-05 19:40:57,800 - BLOCKING - DEBUG - 📊 performance_test_spot 数据更新: symbol=TEST61, 总消息=62, 频率=355.10/s
2025-08-05 19:40:57,800 - BLOCKING - DEBUG - 📊 performance_test_spot 数据更新: symbol=TEST62, 总消息=63, 频率=360.74/s
2025-08-05 19:40:57,800 - BLOCKING - DEBUG - 📊 performance_test_spot 数据更新: symbol=TEST63, 总消息=64, 频率=366.35/s
2025-08-05 19:40:57,800 - BLOCKING - DEBUG - 📊 performance_test_spot 数据更新: symbol=TEST64, 总消息=65, 频率=371.96/s
2025-08-05 19:40:57,800 - BLOCKING - DEBUG - 📊 performance_test_spot 数据更新: symbol=TEST65, 总消息=66, 频率=377.60/s
2025-08-05 19:40:57,800 - BLOCKING - DEBUG - 📊 performance_test_spot 数据更新: symbol=TEST66, 总消息=67, 频率=383.23/s
2025-08-05 19:40:57,800 - BLOCKING - DEBUG - 📊 performance_test_spot 数据更新: symbol=TEST67, 总消息=68, 频率=388.86/s
2025-08-05 19:40:57,800 - BLOCKING - DEBUG - 📊 performance_test_spot 数据更新: symbol=TEST68, 总消息=69, 频率=394.49/s
2025-08-05 19:40:57,800 - BLOCKING - DEBUG - 📊 performance_test_spot 数据更新: symbol=TEST69, 总消息=70, 频率=400.13/s
2025-08-05 19:40:57,800 - BLOCKING - DEBUG - 📊 performance_test_spot 数据更新: symbol=TEST70, 总消息=71, 频率=405.72/s
2025-08-05 19:40:57,800 - BLOCKING - DEBUG - 📊 performance_test_spot 数据更新: symbol=TEST71, 总消息=72, 频率=411.31/s
2025-08-05 19:40:57,800 - BLOCKING - DEBUG - 📊 performance_test_spot 数据更新: symbol=TEST72, 总消息=73, 频率=416.91/s
2025-08-05 19:40:57,800 - BLOCKING - DEBUG - 📊 performance_test_spot 数据更新: symbol=TEST73, 总消息=74, 频率=422.52/s
2025-08-05 19:40:57,800 - BLOCKING - DEBUG - 📊 performance_test_spot 数据更新: symbol=TEST74, 总消息=75, 频率=428.14/s
2025-08-05 19:40:57,800 - BLOCKING - DEBUG - 📊 performance_test_spot 数据更新: symbol=TEST75, 总消息=76, 频率=433.75/s
2025-08-05 19:40:57,800 - BLOCKING - DEBUG - 📊 performance_test_spot 数据更新: symbol=TEST76, 总消息=77, 频率=439.37/s
2025-08-05 19:40:57,800 - BLOCKING - DEBUG - 📊 performance_test_spot 数据更新: symbol=TEST77, 总消息=78, 频率=444.99/s
2025-08-05 19:40:57,800 - BLOCKING - DEBUG - 📊 performance_test_spot 数据更新: symbol=TEST78, 总消息=79, 频率=450.58/s
2025-08-05 19:40:57,800 - BLOCKING - DEBUG - 📊 performance_test_spot 数据更新: symbol=TEST79, 总消息=80, 频率=456.17/s
2025-08-05 19:40:57,800 - BLOCKING - DEBUG - 📊 performance_test_spot 数据更新: symbol=TEST80, 总消息=81, 频率=461.77/s
2025-08-05 19:40:57,800 - BLOCKING - DEBUG - 📊 performance_test_spot 数据更新: symbol=TEST81, 总消息=82, 频率=467.38/s
2025-08-05 19:40:57,800 - BLOCKING - DEBUG - 📊 performance_test_spot 数据更新: symbol=TEST82, 总消息=83, 频率=472.98/s
2025-08-05 19:40:57,801 - BLOCKING - DEBUG - 📊 performance_test_spot 数据更新: symbol=TEST83, 总消息=84, 频率=478.58/s
2025-08-05 19:40:57,801 - BLOCKING - DEBUG - 📊 performance_test_spot 数据更新: symbol=TEST84, 总消息=85, 频率=484.18/s
2025-08-05 19:40:57,801 - BLOCKING - DEBUG - 📊 performance_test_spot 数据更新: symbol=TEST85, 总消息=86, 频率=489.76/s
2025-08-05 19:40:57,801 - BLOCKING - DEBUG - 📊 performance_test_spot 数据更新: symbol=TEST86, 总消息=87, 频率=495.30/s
2025-08-05 19:40:57,801 - BLOCKING - DEBUG - 📊 performance_test_spot 数据更新: symbol=TEST87, 总消息=88, 频率=500.82/s
2025-08-05 19:40:57,801 - BLOCKING - DEBUG - 📊 performance_test_spot 数据更新: symbol=TEST88, 总消息=89, 频率=506.38/s
2025-08-05 19:40:57,801 - BLOCKING - DEBUG - 📊 performance_test_spot 数据更新: symbol=TEST89, 总消息=90, 频率=511.94/s
2025-08-05 19:40:57,801 - BLOCKING - DEBUG - 📊 performance_test_spot 数据更新: symbol=TEST90, 总消息=91, 频率=517.50/s
2025-08-05 19:40:57,801 - BLOCKING - DEBUG - 📊 performance_test_spot 数据更新: symbol=TEST91, 总消息=92, 频率=523.05/s
2025-08-05 19:40:57,801 - BLOCKING - DEBUG - 📊 performance_test_spot 数据更新: symbol=TEST92, 总消息=93, 频率=528.61/s
2025-08-05 19:40:57,801 - BLOCKING - DEBUG - 📊 performance_test_spot 数据更新: symbol=TEST93, 总消息=94, 频率=534.09/s
2025-08-05 19:40:57,801 - BLOCKING - DEBUG - 📊 performance_test_spot 数据更新: symbol=TEST94, 总消息=95, 频率=539.60/s
2025-08-05 19:40:57,801 - BLOCKING - DEBUG - 📊 performance_test_spot 数据更新: symbol=TEST95, 总消息=96, 频率=545.13/s
2025-08-05 19:40:57,801 - BLOCKING - DEBUG - 📊 performance_test_spot 数据更新: symbol=TEST96, 总消息=97, 频率=550.67/s
2025-08-05 19:40:57,801 - BLOCKING - DEBUG - 📊 performance_test_spot 数据更新: symbol=TEST97, 总消息=98, 频率=556.21/s
2025-08-05 19:40:57,801 - BLOCKING - DEBUG - 📊 performance_test_spot 数据更新: symbol=TEST98, 总消息=99, 频率=560.99/s
2025-08-05 19:40:57,802 - BLOCKING - DEBUG - 📊 performance_test_spot 数据更新: symbol=TEST99, 总消息=100, 频率=566.45/s
2025-08-05 19:41:42,386 - BLOCKING - INFO - 🔍 WebSocket数据流阻塞追踪器已启动
2025-08-05 19:41:42,393 - BLOCKING - DEBUG - 📊 gate_spot 数据更新: symbol=BTC-USDT, 总消息=1, 频率=134.04/s
2025-08-05 19:41:42,393 - BLOCKING - DEBUG - 📊 okx_futures 数据更新: symbol=ETH-USDT, 总消息=1, 频率=130.31/s
2025-08-05 19:41:42,393 - BLOCKING - DEBUG - 📊 bybit_spot 数据更新: symbol=SOL-USDT, 总消息=1, 频率=129.45/s
2025-08-05 19:41:42,393 - BLOCKING - DEBUG - 📊 gate_spot 数据更新: symbol=BTC-USDT, 总消息=2, 频率=254.74/s
2025-08-05 19:41:42,393 - BLOCKING - DEBUG - 📊 gate_spot 数据更新: symbol=ETH-USDT, 总消息=3, 频率=379.95/s
2025-08-05 19:41:42,393 - BLOCKING - DEBUG - 📊 gate_spot 数据更新: symbol=SOL-USDT, 总消息=4, 频率=504.52/s
2025-08-05 19:41:42,393 - BLOCKING - DEBUG - 📊 gate_spot 数据更新: symbol=ADA-USDT, 总消息=5, 频率=628.32/s
2025-08-05 19:41:42,393 - BLOCKING - DEBUG - 📊 gate_spot 数据更新: symbol=DOT-USDT, 总消息=6, 频率=751.35/s
2025-08-05 19:41:42,394 - BLOCKING - DEBUG - 📊 okx_spot 数据更新: symbol=BTC-USDT, 总消息=1, 频率=124.78/s
2025-08-05 19:41:42,394 - BLOCKING - DEBUG - 📊 okx_spot 数据更新: symbol=ETH-USDT, 总消息=2, 频率=248.60/s
2025-08-05 19:41:42,394 - BLOCKING - DEBUG - 📊 okx_spot 数据更新: symbol=SOL-USDT, 总消息=3, 频率=371.58/s
2025-08-05 19:41:42,394 - BLOCKING - DEBUG - 📊 okx_spot 数据更新: symbol=ADA-USDT, 总消息=4, 频率=493.64/s
2025-08-05 19:41:42,394 - BLOCKING - DEBUG - 📊 okx_spot 数据更新: symbol=DOT-USDT, 总消息=5, 频率=614.82/s
2025-08-05 19:41:42,394 - BLOCKING - DEBUG - 📊 bybit_spot 数据更新: symbol=BTC-USDT, 总消息=2, 频率=244.77/s
2025-08-05 19:41:42,394 - BLOCKING - DEBUG - 📊 bybit_spot 数据更新: symbol=ETH-USDT, 总消息=3, 频率=365.79/s
2025-08-05 19:41:42,394 - BLOCKING - DEBUG - 📊 bybit_spot 数据更新: symbol=SOL-USDT, 总消息=4, 频率=486.06/s
2025-08-05 19:41:42,394 - BLOCKING - DEBUG - 📊 bybit_spot 数据更新: symbol=ADA-USDT, 总消息=5, 频率=605.50/s
2025-08-05 19:41:42,394 - BLOCKING - DEBUG - 📊 bybit_spot 数据更新: symbol=DOT-USDT, 总消息=6, 频率=724.18/s
2025-08-05 19:41:42,394 - BLOCKING - DEBUG - 📊 test_exchange_spot 数据更新: symbol=BTC-USDT, 总消息=1, 频率=120.01/s
2025-08-05 19:41:42,495 - BLOCKING - DEBUG - 📊 gate_spot 数据更新: symbol=TEST0-USDT, 总消息=7, 频率=63.83/s
2025-08-05 19:41:42,496 - BLOCKING - DEBUG - 📊 okx_spot 数据更新: symbol=TEST0-USDT, 总消息=6, 频率=54.55/s
2025-08-05 19:41:42,496 - BLOCKING - DEBUG - 📊 bybit_spot 数据更新: symbol=TEST0-USDT, 总消息=7, 频率=63.47/s
2025-08-05 19:41:42,497 - BLOCKING - DEBUG - 📊 gate_spot 数据更新: symbol=TEST1-USDT, 总消息=8, 频率=72.05/s
2025-08-05 19:41:42,497 - BLOCKING - DEBUG - 📊 okx_spot 数据更新: symbol=TEST1-USDT, 总消息=7, 频率=62.96/s
2025-08-05 19:41:42,497 - BLOCKING - DEBUG - 📊 bybit_spot 数据更新: symbol=TEST1-USDT, 总消息=8, 频率=71.75/s
2025-08-05 19:41:42,498 - BLOCKING - DEBUG - 📊 gate_spot 数据更新: symbol=TEST2-USDT, 总消息=9, 频率=80.23/s
2025-08-05 19:41:42,498 - BLOCKING - DEBUG - 📊 okx_spot 数据更新: symbol=TEST2-USDT, 总消息=8, 频率=71.19/s
2025-08-05 19:41:42,498 - BLOCKING - DEBUG - 📊 bybit_spot 数据更新: symbol=TEST2-USDT, 总消息=9, 频率=79.87/s
2025-08-05 19:41:42,499 - BLOCKING - DEBUG - 📊 gate_spot 数据更新: symbol=TEST3-USDT, 总消息=10, 频率=88.21/s
2025-08-05 19:41:42,499 - BLOCKING - DEBUG - 📊 okx_spot 数据更新: symbol=TEST3-USDT, 总消息=9, 频率=79.25/s
2025-08-05 19:41:42,499 - BLOCKING - DEBUG - 📊 bybit_spot 数据更新: symbol=TEST3-USDT, 总消息=10, 频率=87.83/s
2025-08-05 19:41:42,500 - BLOCKING - DEBUG - 📊 gate_spot 数据更新: symbol=TEST4-USDT, 总消息=11, 频率=96.01/s
2025-08-05 19:41:42,500 - BLOCKING - DEBUG - 📊 okx_spot 数据更新: symbol=TEST4-USDT, 总消息=10, 频率=87.12/s
2025-08-05 19:41:42,501 - BLOCKING - DEBUG - 📊 bybit_spot 数据更新: symbol=TEST4-USDT, 总消息=11, 频率=95.63/s
2025-08-05 19:41:42,501 - BLOCKING - DEBUG - 📊 gate_spot 数据更新: symbol=TEST5-USDT, 总消息=12, 频率=103.66/s
2025-08-05 19:41:42,501 - BLOCKING - DEBUG - 📊 okx_spot 数据更新: symbol=TEST5-USDT, 总消息=11, 频率=94.86/s
2025-08-05 19:41:42,502 - BLOCKING - DEBUG - 📊 bybit_spot 数据更新: symbol=TEST5-USDT, 总消息=12, 频率=103.25/s
2025-08-05 19:41:42,502 - BLOCKING - DEBUG - 📊 gate_spot 数据更新: symbol=TEST6-USDT, 总消息=13, 频率=111.14/s
2025-08-05 19:41:42,503 - BLOCKING - DEBUG - 📊 okx_spot 数据更新: symbol=TEST6-USDT, 总消息=12, 频率=102.41/s
2025-08-05 19:41:42,503 - BLOCKING - DEBUG - 📊 bybit_spot 数据更新: symbol=TEST6-USDT, 总消息=13, 频率=110.74/s
2025-08-05 19:41:42,504 - BLOCKING - DEBUG - 📊 gate_spot 数据更新: symbol=TEST7-USDT, 总消息=14, 频率=118.45/s
2025-08-05 19:41:42,504 - BLOCKING - DEBUG - 📊 okx_spot 数据更新: symbol=TEST7-USDT, 总消息=13, 频率=109.85/s
2025-08-05 19:41:42,504 - BLOCKING - DEBUG - 📊 bybit_spot 数据更新: symbol=TEST7-USDT, 总消息=14, 频率=118.06/s
2025-08-05 19:41:42,505 - BLOCKING - DEBUG - 📊 gate_spot 数据更新: symbol=TEST8-USDT, 总消息=15, 频率=125.62/s
2025-08-05 19:41:42,505 - BLOCKING - DEBUG - 📊 okx_spot 数据更新: symbol=TEST8-USDT, 总消息=14, 频率=117.13/s
2025-08-05 19:41:42,505 - BLOCKING - DEBUG - 📊 bybit_spot 数据更新: symbol=TEST8-USDT, 总消息=15, 频率=125.25/s
2025-08-05 19:41:42,506 - BLOCKING - DEBUG - 📊 gate_spot 数据更新: symbol=TEST9-USDT, 总消息=16, 频率=132.59/s
2025-08-05 19:41:42,506 - BLOCKING - DEBUG - 📊 okx_spot 数据更新: symbol=TEST9-USDT, 总消息=15, 频率=124.10/s
2025-08-05 19:41:42,506 - BLOCKING - DEBUG - 📊 bybit_spot 数据更新: symbol=TEST9-USDT, 总消息=16, 频率=132.28/s
2025-08-05 19:41:42,507 - BLOCKING - DEBUG - 📊 gate_spot 数据更新: symbol=TEST10-USDT, 总消息=17, 频率=139.42/s
2025-08-05 19:41:42,508 - BLOCKING - DEBUG - 📊 okx_spot 数据更新: symbol=TEST10-USDT, 总消息=16, 频率=131.04/s
2025-08-05 19:41:42,508 - BLOCKING - DEBUG - 📊 bybit_spot 数据更新: symbol=TEST10-USDT, 总消息=17, 频率=139.10/s
2025-08-05 19:41:42,509 - BLOCKING - DEBUG - 📊 gate_spot 数据更新: symbol=TEST11-USDT, 总消息=18, 频率=146.22/s
2025-08-05 19:41:42,509 - BLOCKING - DEBUG - 📊 okx_spot 数据更新: symbol=TEST11-USDT, 总消息=17, 频率=137.70/s
2025-08-05 19:41:42,509 - BLOCKING - DEBUG - 📊 bybit_spot 数据更新: symbol=TEST11-USDT, 总消息=18, 频率=145.70/s
2025-08-05 19:41:42,510 - BLOCKING - DEBUG - 📊 gate_spot 数据更新: symbol=TEST12-USDT, 总消息=19, 频率=152.87/s
2025-08-05 19:41:42,510 - BLOCKING - DEBUG - 📊 okx_spot 数据更新: symbol=TEST12-USDT, 总消息=18, 频率=144.31/s
2025-08-05 19:41:42,510 - BLOCKING - DEBUG - 📊 bybit_spot 数据更新: symbol=TEST12-USDT, 总消息=19, 频率=152.23/s
2025-08-05 19:41:42,511 - BLOCKING - DEBUG - 📊 gate_spot 数据更新: symbol=TEST13-USDT, 总消息=20, 频率=159.41/s
2025-08-05 19:41:42,512 - BLOCKING - DEBUG - 📊 okx_spot 数据更新: symbol=TEST13-USDT, 总消息=19, 频率=150.80/s
2025-08-05 19:41:42,512 - BLOCKING - DEBUG - 📊 bybit_spot 数据更新: symbol=TEST13-USDT, 总消息=20, 频率=158.65/s
2025-08-05 19:41:42,512 - BLOCKING - DEBUG - 📊 gate_spot 数据更新: symbol=TEST14-USDT, 总消息=21, 频率=165.81/s
2025-08-05 19:41:42,513 - BLOCKING - DEBUG - 📊 okx_spot 数据更新: symbol=TEST14-USDT, 总消息=20, 频率=157.15/s
2025-08-05 19:41:42,513 - BLOCKING - DEBUG - 📊 bybit_spot 数据更新: symbol=TEST14-USDT, 总消息=21, 频率=164.86/s
2025-08-05 19:41:42,513 - BLOCKING - DEBUG - 📊 gate_spot 数据更新: symbol=TEST15-USDT, 总消息=22, 频率=172.11/s
2025-08-05 19:41:42,514 - BLOCKING - DEBUG - 📊 okx_spot 数据更新: symbol=TEST15-USDT, 总消息=21, 频率=163.45/s
2025-08-05 19:41:42,514 - BLOCKING - DEBUG - 📊 bybit_spot 数据更新: symbol=TEST15-USDT, 总消息=22, 频率=171.13/s
2025-08-05 19:41:42,515 - BLOCKING - DEBUG - 📊 gate_spot 数据更新: symbol=TEST16-USDT, 总消息=23, 频率=178.26/s
2025-08-05 19:41:42,515 - BLOCKING - DEBUG - 📊 okx_spot 数据更新: symbol=TEST16-USDT, 总消息=22, 频率=169.63/s
2025-08-05 19:41:42,515 - BLOCKING - DEBUG - 📊 bybit_spot 数据更新: symbol=TEST16-USDT, 总消息=23, 频率=177.14/s
2025-08-05 19:41:42,516 - BLOCKING - DEBUG - 📊 gate_spot 数据更新: symbol=TEST17-USDT, 总消息=24, 频率=184.32/s
2025-08-05 19:41:42,516 - BLOCKING - DEBUG - 📊 okx_spot 数据更新: symbol=TEST17-USDT, 总消息=23, 频率=175.76/s
2025-08-05 19:41:42,517 - BLOCKING - DEBUG - 📊 bybit_spot 数据更新: symbol=TEST17-USDT, 总消息=24, 频率=183.05/s
2025-08-05 19:41:42,517 - BLOCKING - DEBUG - 📊 gate_spot 数据更新: symbol=TEST18-USDT, 总消息=25, 频率=190.25/s
2025-08-05 19:41:42,518 - BLOCKING - DEBUG - 📊 okx_spot 数据更新: symbol=TEST18-USDT, 总消息=24, 频率=181.77/s
2025-08-05 19:41:42,518 - BLOCKING - DEBUG - 📊 bybit_spot 数据更新: symbol=TEST18-USDT, 总消息=25, 频率=188.98/s
2025-08-05 19:41:42,518 - BLOCKING - DEBUG - 📊 gate_spot 数据更新: symbol=TEST19-USDT, 总消息=26, 频率=196.07/s
2025-08-05 19:41:42,519 - BLOCKING - DEBUG - 📊 okx_spot 数据更新: symbol=TEST19-USDT, 总消息=25, 频率=187.68/s
2025-08-05 19:41:42,519 - BLOCKING - DEBUG - 📊 bybit_spot 数据更新: symbol=TEST19-USDT, 总消息=26, 频率=194.84/s
2025-08-05 19:41:42,519 - BLOCKING - DEBUG - 📊 gate_spot 数据更新: symbol=TEST20-USDT, 总消息=27, 频率=201.82/s
2025-08-05 19:41:42,520 - BLOCKING - DEBUG - 📊 okx_spot 数据更新: symbol=TEST20-USDT, 总消息=26, 频率=193.48/s
2025-08-05 19:41:42,520 - BLOCKING - DEBUG - 📊 bybit_spot 数据更新: symbol=TEST20-USDT, 总消息=27, 频率=200.55/s
2025-08-05 19:41:42,520 - BLOCKING - DEBUG - 📊 gate_spot 数据更新: symbol=TEST21-USDT, 总消息=28, 频率=207.51/s
2025-08-05 19:41:42,521 - BLOCKING - DEBUG - 📊 okx_spot 数据更新: symbol=TEST21-USDT, 总消息=27, 频率=198.97/s
2025-08-05 19:41:42,521 - BLOCKING - DEBUG - 📊 bybit_spot 数据更新: symbol=TEST21-USDT, 总消息=28, 频率=206.05/s
2025-08-05 19:41:42,522 - BLOCKING - DEBUG - 📊 gate_spot 数据更新: symbol=TEST22-USDT, 总消息=29, 频率=213.07/s
2025-08-05 19:41:42,522 - BLOCKING - DEBUG - 📊 okx_spot 数据更新: symbol=TEST22-USDT, 总消息=28, 频率=204.58/s
2025-08-05 19:41:42,523 - BLOCKING - DEBUG - 📊 bybit_spot 数据更新: symbol=TEST22-USDT, 总消息=29, 频率=211.60/s
2025-08-05 19:41:42,523 - BLOCKING - DEBUG - 📊 gate_spot 数据更新: symbol=TEST23-USDT, 总消息=30, 频率=218.50/s
2025-08-05 19:41:42,524 - BLOCKING - DEBUG - 📊 okx_spot 数据更新: symbol=TEST23-USDT, 总消息=29, 频率=210.11/s
2025-08-05 19:41:42,524 - BLOCKING - DEBUG - 📊 bybit_spot 数据更新: symbol=TEST23-USDT, 总消息=30, 频率=217.04/s
2025-08-05 19:41:42,524 - BLOCKING - DEBUG - 📊 gate_spot 数据更新: symbol=TEST24-USDT, 总消息=31, 频率=223.84/s
2025-08-05 19:41:42,525 - BLOCKING - DEBUG - 📊 okx_spot 数据更新: symbol=TEST24-USDT, 总消息=30, 频率=215.52/s
2025-08-05 19:41:42,525 - BLOCKING - DEBUG - 📊 bybit_spot 数据更新: symbol=TEST24-USDT, 总消息=31, 频率=222.41/s
2025-08-05 19:41:42,525 - BLOCKING - DEBUG - 📊 gate_spot 数据更新: symbol=TEST25-USDT, 总消息=32, 频率=229.09/s
2025-08-05 19:41:42,526 - BLOCKING - DEBUG - 📊 okx_spot 数据更新: symbol=TEST25-USDT, 总消息=31, 频率=220.86/s
2025-08-05 19:41:42,526 - BLOCKING - DEBUG - 📊 bybit_spot 数据更新: symbol=TEST25-USDT, 总消息=32, 频率=227.67/s
2025-08-05 19:41:42,526 - BLOCKING - DEBUG - 📊 gate_spot 数据更新: symbol=TEST26-USDT, 总消息=33, 频率=234.25/s
2025-08-05 19:41:42,527 - BLOCKING - DEBUG - 📊 okx_spot 数据更新: symbol=TEST26-USDT, 总消息=32, 频率=226.12/s
2025-08-05 19:41:42,527 - BLOCKING - DEBUG - 📊 bybit_spot 数据更新: symbol=TEST26-USDT, 总消息=33, 频率=232.89/s
2025-08-05 19:41:42,528 - BLOCKING - DEBUG - 📊 gate_spot 数据更新: symbol=TEST27-USDT, 总消息=34, 频率=239.32/s
2025-08-05 19:41:42,528 - BLOCKING - DEBUG - 📊 okx_spot 数据更新: symbol=TEST27-USDT, 总消息=33, 频率=231.29/s
2025-08-05 19:41:42,528 - BLOCKING - DEBUG - 📊 bybit_spot 数据更新: symbol=TEST27-USDT, 总消息=34, 频率=238.01/s
2025-08-05 19:41:42,529 - BLOCKING - DEBUG - 📊 gate_spot 数据更新: symbol=TEST28-USDT, 总消息=35, 频率=244.31/s
2025-08-05 19:41:42,529 - BLOCKING - DEBUG - 📊 okx_spot 数据更新: symbol=TEST28-USDT, 总消息=34, 频率=236.37/s
2025-08-05 19:41:42,530 - BLOCKING - DEBUG - 📊 bybit_spot 数据更新: symbol=TEST28-USDT, 总消息=35, 频率=243.03/s
2025-08-05 19:41:42,530 - BLOCKING - DEBUG - 📊 gate_spot 数据更新: symbol=TEST29-USDT, 总消息=36, 频率=249.24/s
2025-08-05 19:41:42,531 - BLOCKING - DEBUG - 📊 okx_spot 数据更新: symbol=TEST29-USDT, 总消息=35, 频率=241.34/s
2025-08-05 19:41:42,531 - BLOCKING - DEBUG - 📊 bybit_spot 数据更新: symbol=TEST29-USDT, 总消息=36, 频率=247.91/s
2025-08-05 19:41:42,531 - BLOCKING - DEBUG - 📊 gate_spot 数据更新: symbol=TEST30-USDT, 总消息=37, 频率=254.09/s
2025-08-05 19:41:42,532 - BLOCKING - DEBUG - 📊 okx_spot 数据更新: symbol=TEST30-USDT, 总消息=36, 频率=246.23/s
2025-08-05 19:41:42,532 - BLOCKING - DEBUG - 📊 bybit_spot 数据更新: symbol=TEST30-USDT, 总消息=37, 频率=252.78/s
2025-08-05 19:41:42,532 - BLOCKING - DEBUG - 📊 gate_spot 数据更新: symbol=TEST31-USDT, 总消息=38, 频率=258.89/s
2025-08-05 19:41:42,533 - BLOCKING - DEBUG - 📊 okx_spot 数据更新: symbol=TEST31-USDT, 总消息=37, 频率=251.07/s
2025-08-05 19:41:42,533 - BLOCKING - DEBUG - 📊 bybit_spot 数据更新: symbol=TEST31-USDT, 总消息=38, 频率=257.51/s
2025-08-05 19:41:42,533 - BLOCKING - DEBUG - 📊 gate_spot 数据更新: symbol=TEST32-USDT, 总消息=39, 频率=263.57/s
2025-08-05 19:41:42,534 - BLOCKING - DEBUG - 📊 okx_spot 数据更新: symbol=TEST32-USDT, 总消息=38, 频率=255.84/s
2025-08-05 19:41:42,534 - BLOCKING - DEBUG - 📊 bybit_spot 数据更新: symbol=TEST32-USDT, 总消息=39, 频率=262.21/s
2025-08-05 19:41:42,535 - BLOCKING - DEBUG - 📊 gate_spot 数据更新: symbol=TEST33-USDT, 总消息=40, 频率=268.17/s
2025-08-05 19:41:42,535 - BLOCKING - DEBUG - 📊 okx_spot 数据更新: symbol=TEST33-USDT, 总消息=39, 频率=260.45/s
2025-08-05 19:41:42,536 - BLOCKING - DEBUG - 📊 bybit_spot 数据更新: symbol=TEST33-USDT, 总消息=40, 频率=266.40/s
2025-08-05 19:41:42,536 - BLOCKING - DEBUG - 📊 gate_spot 数据更新: symbol=TEST34-USDT, 总消息=41, 频率=272.59/s
2025-08-05 19:41:42,536 - BLOCKING - DEBUG - 📊 okx_spot 数据更新: symbol=TEST34-USDT, 总消息=40, 频率=265.01/s
2025-08-05 19:41:42,537 - BLOCKING - DEBUG - 📊 bybit_spot 数据更新: symbol=TEST34-USDT, 总消息=41, 频率=270.87/s
2025-08-05 19:41:42,537 - BLOCKING - DEBUG - 📊 gate_spot 数据更新: symbol=TEST35-USDT, 总消息=42, 频率=276.74/s
2025-08-05 19:41:42,538 - BLOCKING - DEBUG - 📊 okx_spot 数据更新: symbol=TEST35-USDT, 总消息=41, 频率=269.52/s
2025-08-05 19:41:42,538 - BLOCKING - DEBUG - 📊 bybit_spot 数据更新: symbol=TEST35-USDT, 总消息=42, 频率=275.35/s
2025-08-05 19:41:42,538 - BLOCKING - DEBUG - 📊 gate_spot 数据更新: symbol=TEST36-USDT, 总消息=43, 频率=281.15/s
2025-08-05 19:41:42,539 - BLOCKING - DEBUG - 📊 okx_spot 数据更新: symbol=TEST36-USDT, 总消息=42, 频率=273.98/s
2025-08-05 19:41:42,539 - BLOCKING - DEBUG - 📊 bybit_spot 数据更新: symbol=TEST36-USDT, 总消息=43, 频率=279.79/s
2025-08-05 19:41:42,540 - BLOCKING - DEBUG - 📊 gate_spot 数据更新: symbol=TEST37-USDT, 总消息=44, 频率=285.56/s
2025-08-05 19:41:42,540 - BLOCKING - DEBUG - 📊 okx_spot 数据更新: symbol=TEST37-USDT, 总消息=43, 频率=278.41/s
2025-08-05 19:41:42,540 - BLOCKING - DEBUG - 📊 bybit_spot 数据更新: symbol=TEST37-USDT, 总消息=44, 频率=284.17/s
2025-08-05 19:41:42,541 - BLOCKING - DEBUG - 📊 gate_spot 数据更新: symbol=TEST38-USDT, 总消息=45, 频率=289.85/s
2025-08-05 19:41:42,541 - BLOCKING - DEBUG - 📊 okx_spot 数据更新: symbol=TEST38-USDT, 总消息=44, 频率=282.74/s
2025-08-05 19:41:42,541 - BLOCKING - DEBUG - 📊 bybit_spot 数据更新: symbol=TEST38-USDT, 总消息=45, 频率=288.51/s
2025-08-05 19:41:42,542 - BLOCKING - DEBUG - 📊 gate_spot 数据更新: symbol=TEST39-USDT, 总消息=46, 频率=294.13/s
2025-08-05 19:41:42,542 - BLOCKING - DEBUG - 📊 okx_spot 数据更新: symbol=TEST39-USDT, 总消息=45, 频率=287.06/s
2025-08-05 19:41:42,543 - BLOCKING - DEBUG - 📊 bybit_spot 数据更新: symbol=TEST39-USDT, 总消息=46, 频率=292.78/s
2025-08-05 19:41:42,543 - BLOCKING - DEBUG - 📊 gate_spot 数据更新: symbol=TEST40-USDT, 总消息=47, 频率=298.29/s
2025-08-05 19:41:42,543 - BLOCKING - DEBUG - 📊 okx_spot 数据更新: symbol=TEST40-USDT, 总消息=46, 频率=291.31/s
2025-08-05 19:41:42,544 - BLOCKING - DEBUG - 📊 bybit_spot 数据更新: symbol=TEST40-USDT, 总消息=47, 频率=296.96/s
2025-08-05 19:41:42,544 - BLOCKING - DEBUG - 📊 gate_spot 数据更新: symbol=TEST41-USDT, 总消息=48, 频率=302.42/s
2025-08-05 19:41:42,545 - BLOCKING - DEBUG - 📊 okx_spot 数据更新: symbol=TEST41-USDT, 总消息=47, 频率=295.50/s
2025-08-05 19:41:42,545 - BLOCKING - DEBUG - 📊 bybit_spot 数据更新: symbol=TEST41-USDT, 总消息=48, 频率=301.10/s
2025-08-05 19:41:42,545 - BLOCKING - DEBUG - 📊 gate_spot 数据更新: symbol=TEST42-USDT, 总消息=49, 频率=306.51/s
2025-08-05 19:41:42,546 - BLOCKING - DEBUG - 📊 okx_spot 数据更新: symbol=TEST42-USDT, 总消息=48, 频率=299.63/s
2025-08-05 19:41:42,546 - BLOCKING - DEBUG - 📊 bybit_spot 数据更新: symbol=TEST42-USDT, 总消息=49, 频率=305.12/s
2025-08-05 19:41:42,547 - BLOCKING - DEBUG - 📊 gate_spot 数据更新: symbol=TEST43-USDT, 总消息=50, 频率=310.54/s
2025-08-05 19:41:42,547 - BLOCKING - DEBUG - 📊 okx_spot 数据更新: symbol=TEST43-USDT, 总消息=49, 频率=303.70/s
2025-08-05 19:41:42,547 - BLOCKING - DEBUG - 📊 bybit_spot 数据更新: symbol=TEST43-USDT, 总消息=50, 频率=309.16/s
2025-08-05 19:41:42,548 - BLOCKING - DEBUG - 📊 gate_spot 数据更新: symbol=TEST44-USDT, 总消息=51, 频率=314.47/s
2025-08-05 19:41:42,548 - BLOCKING - DEBUG - 📊 okx_spot 数据更新: symbol=TEST44-USDT, 总消息=50, 频率=307.72/s
2025-08-05 19:41:42,548 - BLOCKING - DEBUG - 📊 bybit_spot 数据更新: symbol=TEST44-USDT, 总消息=51, 频率=313.08/s
2025-08-05 19:41:42,549 - BLOCKING - DEBUG - 📊 gate_spot 数据更新: symbol=TEST45-USDT, 总消息=52, 频率=318.35/s
2025-08-05 19:41:42,549 - BLOCKING - DEBUG - 📊 okx_spot 数据更新: symbol=TEST45-USDT, 总消息=51, 频率=311.67/s
2025-08-05 19:41:42,550 - BLOCKING - DEBUG - 📊 bybit_spot 数据更新: symbol=TEST45-USDT, 总消息=52, 频率=316.99/s
2025-08-05 19:41:42,550 - BLOCKING - DEBUG - 📊 gate_spot 数据更新: symbol=TEST46-USDT, 总消息=53, 频率=322.20/s
2025-08-05 19:41:42,550 - BLOCKING - DEBUG - 📊 okx_spot 数据更新: symbol=TEST46-USDT, 总消息=52, 频率=315.57/s
2025-08-05 19:41:42,551 - BLOCKING - DEBUG - 📊 bybit_spot 数据更新: symbol=TEST46-USDT, 总消息=53, 频率=320.83/s
2025-08-05 19:41:42,551 - BLOCKING - DEBUG - 📊 gate_spot 数据更新: symbol=TEST47-USDT, 总消息=54, 频率=325.97/s
2025-08-05 19:41:42,551 - BLOCKING - DEBUG - 📊 okx_spot 数据更新: symbol=TEST47-USDT, 总消息=53, 频率=319.42/s
2025-08-05 19:41:42,552 - BLOCKING - DEBUG - 📊 bybit_spot 数据更新: symbol=TEST47-USDT, 总消息=54, 频率=324.61/s
2025-08-05 19:41:42,552 - BLOCKING - DEBUG - 📊 gate_spot 数据更新: symbol=TEST48-USDT, 总消息=55, 频率=329.69/s
2025-08-05 19:41:42,553 - BLOCKING - DEBUG - 📊 okx_spot 数据更新: symbol=TEST48-USDT, 总消息=54, 频率=323.21/s
2025-08-05 19:41:42,553 - BLOCKING - DEBUG - 📊 bybit_spot 数据更新: symbol=TEST48-USDT, 总消息=55, 频率=328.34/s
2025-08-05 19:41:42,553 - BLOCKING - DEBUG - 📊 gate_spot 数据更新: symbol=TEST49-USDT, 总消息=56, 频率=333.38/s
2025-08-05 19:41:42,554 - BLOCKING - DEBUG - 📊 okx_spot 数据更新: symbol=TEST49-USDT, 总消息=55, 频率=326.94/s
2025-08-05 19:41:42,554 - BLOCKING - DEBUG - 📊 bybit_spot 数据更新: symbol=TEST49-USDT, 总消息=56, 频率=332.02/s
2025-08-05 19:41:42,556 - BLOCKING - INFO - 🔗 连接事件: test_gate_spot - data_staleness_detected - {'silent_duration_seconds': 85, 'last_update_time': 1754415617556, 'staleness_threshold_ms': 30000}
2025-08-05 19:41:42,556 - BLOCKING - INFO - 🔗 连接事件: gate_spot - data_staleness_detected - {'silent_duration_seconds': 80.862, 'last_update_time': 1754412617489, 'staleness_threshold_ms': 1000}
2025-08-05 19:41:42,556 - BLOCKING - INFO - 🔗 连接事件: okx_spot - data_staleness_detected - {'silent_duration_seconds': 86.237, 'last_update_time': 1754412611601, 'staleness_threshold_ms': 1000}
2025-08-05 19:41:42,556 - BLOCKING - DEBUG - 📊 performance_test_spot 数据更新: symbol=TEST0, 总消息=1, 频率=5.87/s
2025-08-05 19:41:42,556 - BLOCKING - DEBUG - 📊 performance_test_spot 数据更新: symbol=TEST1, 总消息=2, 频率=11.73/s
2025-08-05 19:41:42,556 - BLOCKING - DEBUG - 📊 performance_test_spot 数据更新: symbol=TEST2, 总消息=3, 频率=17.59/s
2025-08-05 19:41:42,556 - BLOCKING - DEBUG - 📊 performance_test_spot 数据更新: symbol=TEST3, 总消息=4, 频率=23.45/s
2025-08-05 19:41:42,556 - BLOCKING - DEBUG - 📊 performance_test_spot 数据更新: symbol=TEST4, 总消息=5, 频率=29.30/s
2025-08-05 19:41:42,556 - BLOCKING - DEBUG - 📊 performance_test_spot 数据更新: symbol=TEST5, 总消息=6, 频率=35.16/s
2025-08-05 19:41:42,556 - BLOCKING - DEBUG - 📊 performance_test_spot 数据更新: symbol=TEST6, 总消息=7, 频率=41.01/s
2025-08-05 19:41:42,556 - BLOCKING - DEBUG - 📊 performance_test_spot 数据更新: symbol=TEST7, 总消息=8, 频率=46.86/s
2025-08-05 19:41:42,556 - BLOCKING - DEBUG - 📊 performance_test_spot 数据更新: symbol=TEST8, 总消息=9, 频率=52.71/s
2025-08-05 19:41:42,556 - BLOCKING - DEBUG - 📊 performance_test_spot 数据更新: symbol=TEST9, 总消息=10, 频率=58.55/s
2025-08-05 19:41:42,556 - BLOCKING - DEBUG - 📊 performance_test_spot 数据更新: symbol=TEST10, 总消息=11, 频率=64.39/s
2025-08-05 19:41:42,556 - BLOCKING - DEBUG - 📊 performance_test_spot 数据更新: symbol=TEST11, 总消息=12, 频率=70.23/s
2025-08-05 19:41:42,556 - BLOCKING - DEBUG - 📊 performance_test_spot 数据更新: symbol=TEST12, 总消息=13, 频率=76.07/s
2025-08-05 19:41:42,556 - BLOCKING - DEBUG - 📊 performance_test_spot 数据更新: symbol=TEST13, 总消息=14, 频率=81.90/s
2025-08-05 19:41:42,556 - BLOCKING - DEBUG - 📊 performance_test_spot 数据更新: symbol=TEST14, 总消息=15, 频率=87.74/s
2025-08-05 19:41:42,556 - BLOCKING - DEBUG - 📊 performance_test_spot 数据更新: symbol=TEST15, 总消息=16, 频率=93.57/s
2025-08-05 19:41:42,557 - BLOCKING - DEBUG - 📊 performance_test_spot 数据更新: symbol=TEST16, 总消息=17, 频率=99.40/s
2025-08-05 19:41:42,557 - BLOCKING - DEBUG - 📊 performance_test_spot 数据更新: symbol=TEST17, 总消息=18, 频率=105.23/s
2025-08-05 19:41:42,557 - BLOCKING - DEBUG - 📊 performance_test_spot 数据更新: symbol=TEST18, 总消息=19, 频率=111.05/s
2025-08-05 19:41:42,557 - BLOCKING - DEBUG - 📊 performance_test_spot 数据更新: symbol=TEST19, 总消息=20, 频率=116.87/s
2025-08-05 19:41:42,557 - BLOCKING - DEBUG - 📊 performance_test_spot 数据更新: symbol=TEST20, 总消息=21, 频率=122.68/s
2025-08-05 19:41:42,557 - BLOCKING - DEBUG - 📊 performance_test_spot 数据更新: symbol=TEST21, 总消息=22, 频率=128.50/s
2025-08-05 19:41:42,557 - BLOCKING - DEBUG - 📊 performance_test_spot 数据更新: symbol=TEST22, 总消息=23, 频率=134.31/s
2025-08-05 19:41:42,557 - BLOCKING - DEBUG - 📊 performance_test_spot 数据更新: symbol=TEST23, 总消息=24, 频率=140.12/s
2025-08-05 19:41:42,557 - BLOCKING - DEBUG - 📊 performance_test_spot 数据更新: symbol=TEST24, 总消息=25, 频率=145.93/s
2025-08-05 19:41:42,557 - BLOCKING - DEBUG - 📊 performance_test_spot 数据更新: symbol=TEST25, 总消息=26, 频率=151.74/s
2025-08-05 19:41:42,557 - BLOCKING - DEBUG - 📊 performance_test_spot 数据更新: symbol=TEST26, 总消息=27, 频率=157.55/s
2025-08-05 19:41:42,557 - BLOCKING - DEBUG - 📊 performance_test_spot 数据更新: symbol=TEST27, 总消息=28, 频率=163.35/s
2025-08-05 19:41:42,557 - BLOCKING - DEBUG - 📊 performance_test_spot 数据更新: symbol=TEST28, 总消息=29, 频率=169.16/s
2025-08-05 19:41:42,557 - BLOCKING - DEBUG - 📊 performance_test_spot 数据更新: symbol=TEST29, 总消息=30, 频率=174.94/s
2025-08-05 19:41:42,557 - BLOCKING - DEBUG - 📊 performance_test_spot 数据更新: symbol=TEST30, 总消息=31, 频率=180.73/s
2025-08-05 19:41:42,557 - BLOCKING - DEBUG - 📊 performance_test_spot 数据更新: symbol=TEST31, 总消息=32, 频率=186.52/s
2025-08-05 19:41:42,557 - BLOCKING - DEBUG - 📊 performance_test_spot 数据更新: symbol=TEST32, 总消息=33, 频率=192.31/s
2025-08-05 19:41:42,557 - BLOCKING - DEBUG - 📊 performance_test_spot 数据更新: symbol=TEST33, 总消息=34, 频率=198.10/s
2025-08-05 19:41:42,557 - BLOCKING - DEBUG - 📊 performance_test_spot 数据更新: symbol=TEST34, 总消息=35, 频率=203.89/s
2025-08-05 19:41:42,557 - BLOCKING - DEBUG - 📊 performance_test_spot 数据更新: symbol=TEST35, 总消息=36, 频率=209.68/s
2025-08-05 19:41:42,557 - BLOCKING - DEBUG - 📊 performance_test_spot 数据更新: symbol=TEST36, 总消息=37, 频率=215.46/s
2025-08-05 19:41:42,557 - BLOCKING - DEBUG - 📊 performance_test_spot 数据更新: symbol=TEST37, 总消息=38, 频率=221.25/s
2025-08-05 19:41:42,557 - BLOCKING - DEBUG - 📊 performance_test_spot 数据更新: symbol=TEST38, 总消息=39, 频率=227.03/s
2025-08-05 19:41:42,557 - BLOCKING - DEBUG - 📊 performance_test_spot 数据更新: symbol=TEST39, 总消息=40, 频率=232.80/s
2025-08-05 19:41:42,557 - BLOCKING - DEBUG - 📊 performance_test_spot 数据更新: symbol=TEST40, 总消息=41, 频率=238.58/s
2025-08-05 19:41:42,557 - BLOCKING - DEBUG - 📊 performance_test_spot 数据更新: symbol=TEST41, 总消息=42, 频率=244.34/s
2025-08-05 19:41:42,557 - BLOCKING - DEBUG - 📊 performance_test_spot 数据更新: symbol=TEST42, 总消息=43, 频率=250.11/s
2025-08-05 19:41:42,557 - BLOCKING - DEBUG - 📊 performance_test_spot 数据更新: symbol=TEST43, 总消息=44, 频率=255.88/s
2025-08-05 19:41:42,557 - BLOCKING - DEBUG - 📊 performance_test_spot 数据更新: symbol=TEST44, 总消息=45, 频率=261.65/s
2025-08-05 19:41:42,558 - BLOCKING - DEBUG - 📊 performance_test_spot 数据更新: symbol=TEST45, 总消息=46, 频率=267.41/s
2025-08-05 19:41:42,558 - BLOCKING - DEBUG - 📊 performance_test_spot 数据更新: symbol=TEST46, 总消息=47, 频率=273.17/s
2025-08-05 19:41:42,558 - BLOCKING - DEBUG - 📊 performance_test_spot 数据更新: symbol=TEST47, 总消息=48, 频率=278.93/s
2025-08-05 19:41:42,558 - BLOCKING - DEBUG - 📊 performance_test_spot 数据更新: symbol=TEST48, 总消息=49, 频率=284.69/s
2025-08-05 19:41:42,558 - BLOCKING - DEBUG - 📊 performance_test_spot 数据更新: symbol=TEST49, 总消息=50, 频率=290.45/s
2025-08-05 19:41:42,558 - BLOCKING - DEBUG - 📊 performance_test_spot 数据更新: symbol=TEST50, 总消息=51, 频率=296.18/s
2025-08-05 19:41:42,558 - BLOCKING - DEBUG - 📊 performance_test_spot 数据更新: symbol=TEST51, 总消息=52, 频率=301.92/s
2025-08-05 19:41:42,558 - BLOCKING - DEBUG - 📊 performance_test_spot 数据更新: symbol=TEST52, 总消息=53, 频率=307.66/s
2025-08-05 19:41:42,558 - BLOCKING - DEBUG - 📊 performance_test_spot 数据更新: symbol=TEST53, 总消息=54, 频率=313.41/s
2025-08-05 19:41:42,558 - BLOCKING - DEBUG - 📊 performance_test_spot 数据更新: symbol=TEST54, 总消息=55, 频率=319.16/s
2025-08-05 19:41:42,558 - BLOCKING - DEBUG - 📊 performance_test_spot 数据更新: symbol=TEST55, 总消息=56, 频率=324.90/s
2025-08-05 19:41:42,558 - BLOCKING - DEBUG - 📊 performance_test_spot 数据更新: symbol=TEST56, 总消息=57, 频率=330.64/s
2025-08-05 19:41:42,558 - BLOCKING - DEBUG - 📊 performance_test_spot 数据更新: symbol=TEST57, 总消息=58, 频率=336.38/s
2025-08-05 19:41:42,558 - BLOCKING - DEBUG - 📊 performance_test_spot 数据更新: symbol=TEST58, 总消息=59, 频率=342.11/s
2025-08-05 19:41:42,558 - BLOCKING - DEBUG - 📊 performance_test_spot 数据更新: symbol=TEST59, 总消息=60, 频率=347.43/s
2025-08-05 19:41:42,558 - BLOCKING - DEBUG - 📊 performance_test_spot 数据更新: symbol=TEST60, 总消息=61, 频率=352.96/s
2025-08-05 19:41:42,558 - BLOCKING - DEBUG - 📊 performance_test_spot 数据更新: symbol=TEST61, 总消息=62, 频率=358.48/s
2025-08-05 19:41:42,559 - BLOCKING - DEBUG - 📊 performance_test_spot 数据更新: symbol=TEST62, 总消息=63, 频率=363.99/s
2025-08-05 19:41:42,559 - BLOCKING - DEBUG - 📊 performance_test_spot 数据更新: symbol=TEST63, 总消息=64, 频率=369.48/s
2025-08-05 19:41:42,559 - BLOCKING - DEBUG - 📊 performance_test_spot 数据更新: symbol=TEST64, 总消息=65, 频率=374.94/s
2025-08-05 19:41:42,559 - BLOCKING - DEBUG - 📊 performance_test_spot 数据更新: symbol=TEST65, 总消息=66, 频率=380.35/s
2025-08-05 19:41:42,559 - BLOCKING - DEBUG - 📊 performance_test_spot 数据更新: symbol=TEST66, 总消息=67, 频率=385.81/s
2025-08-05 19:41:42,559 - BLOCKING - DEBUG - 📊 performance_test_spot 数据更新: symbol=TEST67, 总消息=68, 频率=391.28/s
2025-08-05 19:41:42,559 - BLOCKING - DEBUG - 📊 performance_test_spot 数据更新: symbol=TEST68, 总消息=69, 频率=396.74/s
2025-08-05 19:41:42,560 - BLOCKING - DEBUG - 📊 performance_test_spot 数据更新: symbol=TEST69, 总消息=70, 频率=402.19/s
2025-08-05 19:41:42,560 - BLOCKING - DEBUG - 📊 performance_test_spot 数据更新: symbol=TEST70, 总消息=71, 频率=407.62/s
2025-08-05 19:41:42,560 - BLOCKING - DEBUG - 📊 performance_test_spot 数据更新: symbol=TEST71, 总消息=72, 频率=413.04/s
2025-08-05 19:41:42,560 - BLOCKING - DEBUG - 📊 performance_test_spot 数据更新: symbol=TEST72, 总消息=73, 频率=418.44/s
2025-08-05 19:41:42,560 - BLOCKING - DEBUG - 📊 performance_test_spot 数据更新: symbol=TEST73, 总消息=74, 频率=423.80/s
2025-08-05 19:41:42,560 - BLOCKING - DEBUG - 📊 performance_test_spot 数据更新: symbol=TEST74, 总消息=75, 频率=429.21/s
2025-08-05 19:41:42,560 - BLOCKING - DEBUG - 📊 performance_test_spot 数据更新: symbol=TEST75, 总消息=76, 频率=434.60/s
2025-08-05 19:41:42,561 - BLOCKING - DEBUG - 📊 performance_test_spot 数据更新: symbol=TEST76, 总消息=77, 频率=439.99/s
2025-08-05 19:41:42,561 - BLOCKING - DEBUG - 📊 performance_test_spot 数据更新: symbol=TEST77, 总消息=78, 频率=445.37/s
2025-08-05 19:41:42,561 - BLOCKING - DEBUG - 📊 performance_test_spot 数据更新: symbol=TEST78, 总消息=79, 频率=450.91/s
2025-08-05 19:41:42,561 - BLOCKING - DEBUG - 📊 performance_test_spot 数据更新: symbol=TEST79, 总消息=80, 频率=456.51/s
2025-08-05 19:41:42,561 - BLOCKING - DEBUG - 📊 performance_test_spot 数据更新: symbol=TEST80, 总消息=81, 频率=462.13/s
2025-08-05 19:41:42,561 - BLOCKING - DEBUG - 📊 performance_test_spot 数据更新: symbol=TEST81, 总消息=82, 频率=467.75/s
2025-08-05 19:41:42,561 - BLOCKING - DEBUG - 📊 performance_test_spot 数据更新: symbol=TEST82, 总消息=83, 频率=473.36/s
2025-08-05 19:41:42,561 - BLOCKING - DEBUG - 📊 performance_test_spot 数据更新: symbol=TEST83, 总消息=84, 频率=478.98/s
2025-08-05 19:41:42,561 - BLOCKING - DEBUG - 📊 performance_test_spot 数据更新: symbol=TEST84, 总消息=85, 频率=484.59/s
2025-08-05 19:41:42,561 - BLOCKING - DEBUG - 📊 performance_test_spot 数据更新: symbol=TEST85, 总消息=86, 频率=490.20/s
2025-08-05 19:41:42,561 - BLOCKING - DEBUG - 📊 performance_test_spot 数据更新: symbol=TEST86, 总消息=87, 频率=495.73/s
2025-08-05 19:41:42,561 - BLOCKING - DEBUG - 📊 performance_test_spot 数据更新: symbol=TEST87, 总消息=88, 频率=501.33/s
2025-08-05 19:41:42,561 - BLOCKING - DEBUG - 📊 performance_test_spot 数据更新: symbol=TEST88, 总消息=89, 频率=506.93/s
2025-08-05 19:41:42,561 - BLOCKING - DEBUG - 📊 performance_test_spot 数据更新: symbol=TEST89, 总消息=90, 频率=512.53/s
2025-08-05 19:41:42,561 - BLOCKING - DEBUG - 📊 performance_test_spot 数据更新: symbol=TEST90, 总消息=91, 频率=518.13/s
2025-08-05 19:41:42,561 - BLOCKING - DEBUG - 📊 performance_test_spot 数据更新: symbol=TEST91, 总消息=92, 频率=523.72/s
2025-08-05 19:41:42,561 - BLOCKING - DEBUG - 📊 performance_test_spot 数据更新: symbol=TEST92, 总消息=93, 频率=529.31/s
2025-08-05 19:41:42,561 - BLOCKING - DEBUG - 📊 performance_test_spot 数据更新: symbol=TEST93, 总消息=94, 频率=534.91/s
2025-08-05 19:41:42,561 - BLOCKING - DEBUG - 📊 performance_test_spot 数据更新: symbol=TEST94, 总消息=95, 频率=540.50/s
2025-08-05 19:41:42,561 - BLOCKING - DEBUG - 📊 performance_test_spot 数据更新: symbol=TEST95, 总消息=96, 频率=546.09/s
2025-08-05 19:41:42,561 - BLOCKING - DEBUG - 📊 performance_test_spot 数据更新: symbol=TEST96, 总消息=97, 频率=551.67/s
2025-08-05 19:41:42,561 - BLOCKING - DEBUG - 📊 performance_test_spot 数据更新: symbol=TEST97, 总消息=98, 频率=557.26/s
2025-08-05 19:41:42,561 - BLOCKING - DEBUG - 📊 performance_test_spot 数据更新: symbol=TEST98, 总消息=99, 频率=562.85/s
2025-08-05 19:41:42,561 - BLOCKING - DEBUG - 📊 performance_test_spot 数据更新: symbol=TEST99, 总消息=100, 频率=568.43/s
