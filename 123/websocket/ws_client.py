"""
WebSocket客户端基类
为所有交易所WebSocket客户端提供基础功能
"""

import asyncio
import json
import logging
import time
from typing import Dict, Any, List, Callable, Optional
from abc import ABC, abstractmethod
import websockets
import sys
import os
from datetime import datetime
import ssl
import inspect

# 尝试导入自定义日志系统
try:
    sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    from utils.logger import get_logger
    # 获取websocket模块的日志器
    logger = get_logger("websocket")
    USE_CUSTOM_LOGGER = True
except ImportError:
    # 如果没有找到自定义日志系统，使用标准日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    logger = logging.getLogger("websocket")
    USE_CUSTOM_LOGGER = False


class WebSocketClient(ABC):
    """WebSocket客户端基类
    :param exchange_name: 交易所名称
    :param settings: 全局配置实例（推荐，所有参数从settings读取）
    """
    
    def __init__(self, exchange_name: str, settings=None):
        self.exchange_name = exchange_name
        self.ws = None
        self.running = False
        self.callbacks = {}
        self.last_message_time = time.time()
        self.connect_time = 0
        self.reconnect_count = 0
        
        # 🔥 **统一集成**：使用统一连接池管理器，避免重复逻辑
        self._integrated_with_pool = False
        self.pool_connection_id = None
        
        # 参数合规化
        if settings is not None and hasattr(settings, 'system'):
            self.max_reconnect_attempts = getattr(settings.system, 'ws_reconnect_max_attempts', 15)
            self.heartbeat_interval = getattr(settings.system, 'ws_heartbeat_interval', 20)
            self.connection_timeout = getattr(settings.system, 'ws_connect_timeout', 15)
            self.connect_warning_threshold = getattr(settings.system, 'ws_connect_timeout', 1.0) / 1000.0
            self.reconnect_delay = getattr(settings.system, 'ws_reconnect_delay', 1.5)
            self.stats_interval = getattr(settings.system, 'ws_stats_interval', 60)
            self.health_check_interval = getattr(settings.system, 'ws_health_check_interval', 30)
            # 🔥 修复：移除重复的silent_duration逻辑，由enhanced_blocking_tracker统一处理
            # self.max_silent_duration = getattr(settings.system, 'ws_max_silent_duration', 45)
            self.auto_recovery_enabled = getattr(settings.system, 'ws_auto_recovery', True)
        else:
            # 兼容测试/无settings用法
            self.max_reconnect_attempts = 15
            self.heartbeat_interval = 20
            self.connection_timeout = 15
            self.connect_warning_threshold = 1.0
            self.reconnect_delay = 1.5
            self.stats_interval = 60
            self.health_check_interval = 30
            # 🔥 修复：移除重复的silent_duration逻辑，由enhanced_blocking_tracker统一处理
            # self.max_silent_duration = 45
            self.auto_recovery_enabled = True
            
        self.message_count = 0
        self.error_count = 0
        self.last_stats_time = time.time()
        self.heartbeat_task = None
        self.reconnect_lock = asyncio.Lock()

        # 🔥 **统一错误处理**：集成统一错误处理器，避免重复实现
        try:
            from websocket.error_handler import get_unified_error_handler
            self.error_handler = get_unified_error_handler()
        except ImportError:
            self.error_handler = None
            self._log_warning("统一错误处理器不可用")

        self._log_info(f"初始化{exchange_name} WebSocket客户端")

        # 重连日志文件
        self.reconnect_log_file = f"logs/websocket_reconnect_{datetime.now().strftime('%Y%m%d')}.log"
        
        # 🔥 **集成统一连接池**：初始化时立即集成，避免后续重复逻辑
        self._initialize_unified_integration()
    
    def _initialize_unified_integration(self):
        """🔥 **统一集成初始化**：整合连接池、错误处理、性能监控"""
        try:
            # 标记已集成，避免重复初始化
            self._integrated_with_pool = True
            self._log_info("✅ 统一连接池集成初始化完成")
        except Exception as e:
            self._log_warning(f"⚠️ 统一集成初始化失败: {e}")
            self._integrated_with_pool = False
    
    def _log_debug(self, message: str):
        """输出调试日志 - 适度启用关键诊断信息"""
        # 🔥 修复：只记录关键的诊断信息，避免日志泛滥
        if any(keyword in message.lower() for keyword in ['队列延迟', 'websocket接收', '连接状态', '时间戳', '数据流']):
            logger.debug(f"[{self.exchange_name}] {message}")

    def _log_info(self, message: str):
        """输出信息日志 - VPS模式：只记录关键信息"""
        # 🔥 VPS修复：只记录关键信息，避免刷屏
        if any(keyword in message for keyword in ['连接成功', '订阅完成', '错误', '失败', '启动', '初始化']):
            logger.info(f"[{self.exchange_name}] {message}")

    def _log_warning(self, message: str):
        """输出警告日志"""
        logger.warning(f"[{self.exchange_name}] {message}")
        # 🔥 修复：增强重连和掉线日志记录
        if any(keyword in message for keyword in ['重连', '连接关闭', '连接失败', '掉线', 'reconnect', 'disconnect']):
            self._log_to_reconnect_file(f"WARNING: {message}")

    def _log_error(self, message: str, exc_info=None):
        """输出错误日志"""
        if exc_info:
            logger.error(f"[{self.exchange_name}] {message}", exc_info=exc_info)
        else:
            logger.error(f"[{self.exchange_name}] {message}")
        # 🔥 修复：增强重连和掉线错误日志记录
        if any(keyword in message for keyword in ['重连', '连接', '掉线', 'reconnect', 'disconnect', 'connection']):
            self._log_to_reconnect_file(f"ERROR: {message}")

    def _log_to_reconnect_file(self, message: str):
        """记录重连相关日志到专门文件"""
        try:
            # 确保日志目录存在
            os.makedirs("logs", exist_ok=True)

            timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S.%f')[:-3]
            log_entry = f"{timestamp} [{self.exchange_name}] {message}\n"

            with open(self.reconnect_log_file, 'a', encoding='utf-8') as f:
                f.write(log_entry)
        except Exception as e:
            # 避免日志记录失败影响主流程
            pass


    
    def register_callback(self, event: str, callback: Callable):
        """
        注册回调函数
        :param event: 事件名称
        :param callback: 回调函数
        """
        if event not in self.callbacks:
            self.callbacks[event] = []
        self.callbacks[event].append(callback)
        self._log_debug(f"已注册 {event} 的回调函数")
    
    def emit(self, event: str, data: Dict[str, Any]):
        """
        触发事件
        :param event: 事件名称
        :param data: 事件数据
        """
        # 🔥 VPS修复：完全删除所有DEBUG print语句，避免终端刷屏

        if event in self.callbacks:
            for callback in self.callbacks[event]:
                try:
                    # 检查是否是协程函数
                    if inspect.iscoroutinefunction(callback):
                        # 如果是协程函数，使用create_task来异步执行
                        asyncio.create_task(callback(data))
                    else:
                        # 普通函数直接调用
                        callback(data)
                except Exception as e:
                    self._log_error(f"执行 {event} 回调时出错: {str(e)}", exc_info=True)
                    self.error_count += 1
                    # 🔥 新增：使用统一错误处理器
                    if self.error_handler:
                        asyncio.create_task(self.error_handler.handle_error(
                            self.exchange_name, e, context={"event": event, "callback": callback.__name__}
                        ))
    
    @abstractmethod
    def get_ws_url(self) -> str:
        """
        获取WebSocket URL
        :return: WebSocket URL
        """
        pass
    
    @abstractmethod
    async def subscribe_channels(self):
        """订阅频道"""
        pass
    
    @abstractmethod
    async def handle_message(self, message: Dict[str, Any]):
        """
        处理消息
        :param message: 消息内容
        """
        pass
    
    @abstractmethod
    async def send_heartbeat(self):
        """发送心跳"""
        pass
    
    async def send(self, message):
        """
        发送WebSocket消息
        
        Args:
            message: 要发送的消息对象（字典、列表或字符串）
            
        Returns:
            bool: 发送是否成功
        """
        if not self.ws or not self.ws.open:
            self._log_warning("WebSocket未连接，无法发送消息")
            return False
            
        try:
            # 如果消息是字典或列表，则转换为JSON字符串
            if isinstance(message, (dict, list)):
                message_str = json.dumps(message)
            else:
                message_str = str(message)
                
            await self.ws.send(message_str)
            self._log_debug(f"发送消息: {message_str[:100]}...")
            return True
        except Exception as e:
            self._log_error(f"发送消息失败: {str(e)}", exc_info=True)
            return False
    
    def _log_stats(self):
        """记录统计信息"""
        now = time.time()
        elapsed = now - self.last_stats_time
        if elapsed > 0:
            self._log_debug(f"消息统计: 总计={self.message_count}, 错误={self.error_count}")
            self.message_count = 0
            self.error_count = 0
            self.last_stats_time = now
    
    async def _connect(self):
        """建立WebSocket连接"""
        if self.ws is not None:
            try:
                await self.ws.close()
            except Exception as e:
                self._log_warning(f"关闭现有连接时出错: {e}")
            finally:
                self.ws = None

        url = self.get_ws_url()
        self._log_info(f"正在连接到 {url}")
        
        try:
            # 设置SSL上下文
            ssl_context = ssl.create_default_context()
            ssl_context.check_hostname = False
            ssl_context.verify_mode = ssl.CERT_NONE
            
            # 直接使用await连接
            start_time = time.time()
            
            try:
                # 使用asyncio.wait_for设置连接超时
                self.ws = await asyncio.wait_for(
                    websockets.connect(
                        url,
                        ssl=ssl_context,
                        ping_interval=None,  # 禁用自动ping，由我们的心跳机制控制
                        ping_timeout=None,
                        close_timeout=5,  # 关闭超时5秒
                        max_size=10 * 1024 * 1024  # 10MB消息大小限制
                    ),
                    timeout=self.connection_timeout
                )
                
                self.connect_time = time.time() - start_time
                
                # 检查连接是否超过警告阈值
                if self.connect_time > self.connect_warning_threshold:
                    self._log_warning(f"连接建立时间过长: {self.connect_time:.3f}秒，超过目标{self.connect_warning_threshold}秒")
                else:
                    self._log_info(f"连接成功，耗时: {self.connect_time:.3f}秒")
                
                # 连接成功，重置计数器和状态
                self.reconnect_count = 0
                self.running = True
                self.last_message_time = time.time()
                
                # 🔥 **关键修复**：连接成功后注册到统一连接池管理器
                try:
                    from websocket.unified_connection_pool_manager import get_connection_pool_manager
                    pool_manager = get_connection_pool_manager()
                    connection_id = await pool_manager.create_connection(
                        exchange=self.exchange_name.lower(),
                        market_type="spot",  # 默认spot，子类可重写
                        client=self
                    )
                    if connection_id:
                        # 🔥 **关键修复**：保存实际的连接ID用于后续重连
                        self.pool_connection_id = connection_id
                        self._log_info(f"✅ 已注册到连接池管理器: {connection_id}")
                    else:
                        self._log_warning("⚠️ 连接池管理器注册失败，但继续运行")
                except Exception as reg_e:
                    self._log_warning(f"⚠️ 连接池管理器注册异常: {reg_e}，但继续运行")
                
                # 订阅频道
                try:
                    # 🔥 修复：根据网络延迟调整订阅超时时间
                    if self.exchange_name == "GATE":
                        # Gate.io网络延迟高，需要更长时间
                        subscribe_timeout = 25  # 25秒订阅超时
                    else:
                        subscribe_timeout = 15   # 其他交易所15秒

                    success = await asyncio.wait_for(
                        self.subscribe_channels(),
                        timeout=subscribe_timeout
                    )
                    if not success:
                        self._log_warning("频道订阅失败，将重连")
                        await self.ws.close()
                        return False
                except asyncio.TimeoutError:
                    self._log_error(f"频道订阅超时（{subscribe_timeout}秒）")
                    await self.ws.close()
                    return False
                
                return True
                
            except asyncio.TimeoutError:
                self._log_error(f"连接超时，超过{self.connection_timeout}秒")
                self.error_count += 1
                return False
                
        except Exception as e:
            self.connect_time = time.time() - start_time if 'start_time' in locals() else 0
            self._log_error(f"连接错误: {e}", exc_info=True)
            self.error_count += 1

            # 🔥 新增：使用统一错误处理器
            if self.error_handler:
                context = {
                    "reconnect_callback": self._reconnect,
                    "url": self.get_ws_url(),
                    "connect_time": self.connect_time
                }
                asyncio.create_task(self.error_handler.handle_error(self.exchange_name, e, context=context))

            return False
    
    async def _reconnect(self):
        """🔥 **统一重连机制**：完全委托给统一连接池管理器，消除重复逻辑"""
        try:
            # 🔥 **关键整合**：所有重连逻辑统一委托给连接池管理器
            from websocket.unified_connection_pool_manager import get_connection_pool_manager

            pool_manager = get_connection_pool_manager()
            
            # 🔥 使用实际的连接ID或构造兜底ID
            if self.pool_connection_id:
                connection_id = self.pool_connection_id
                self._log_info(f"使用已注册连接ID: {connection_id}")
            else:
                # 兜底：构造标准格式连接ID
                connection_id = f"{self.exchange_name.lower()}_websocket"
                self._log_warning(f"⚠️ 未找到注册连接ID，使用标准格式: {connection_id}")

            # 🔥 **核心整合**：完全委托给连接池管理器处理重连
            success = await pool_manager.handle_connection_issue(connection_id, "reconnect_requested")

            if success:
                self._log_info("✅ 统一连接池管理器重连成功")
                self.last_message_time = time.time()
                self.reconnect_count = 0
                return True
            else:
                self._log_error("❌ 统一连接池管理器重连失败")
                self.reconnect_count += 1
                return False

        except Exception as e:
            self._log_error(f"委托重连异常: {e}")
            # 🔥 **最后兜底**：如果连接池不可用，使用最简重连
            return await self._fallback_reconnect()

    async def _fallback_reconnect(self):
        """🔥 **最后兜底**：连接池不可用时的最简重连逻辑"""
        try:
            self._log_warning("使用兜底重连机制")
            
            # 关闭现有连接
            if self.ws:
                try:
                    await self.ws.close()
                except:
                    pass
                self.ws = None

            # 等待后重新连接
            await asyncio.sleep(2.0)
            connected = await self._connect()

            if connected:
                self.last_message_time = time.time()
                self.reconnect_count = 0
                self._log_info("✅ 兜底重连成功")
            else:
                self.reconnect_count += 1
                self._log_error("❌ 兜底重连失败")

            return connected

        except Exception as e:
            self._log_error(f"兜底重连异常: {e}")
            return False
    
    async def _pre_connection_check(self):
        """🔥 完美修复：重连前的网络检查"""
        try:
            # 简单的网络连通性检查
            import socket
            
            # 获取WebSocket URL的主机名
            url = self.get_ws_url()
            if "://" in url:
                host = url.split("://")[1].split("/")[0].split(":")[0]
            else:
                host = url.split("/")[0].split(":")[0]
            
            # 尝试DNS解析
            try:
                socket.gethostbyname(host)
                self._log_debug(f"网络检查通过: {host}")
            except socket.gaierror as e:
                self._log_warning(f"DNS解析失败: {host} - {e}")
                # 等待网络恢复
                await asyncio.sleep(5)
                
        except Exception as e:
            self._log_debug(f"网络检查异常: {e}")
    
    async def _post_reconnection_health_check(self):
        """🔥 完美修复：重连后的健康检查"""
        try:
            # 等待连接稳定
            await asyncio.sleep(3)
            
            # 检查连接状态
            if not self.ws or not self.ws.open:
                self._log_warning("重连后健康检查失败：连接未建立")
                return False
            
            # 发送测试心跳
            heartbeat_success = await self.send_heartbeat()
            if not heartbeat_success:
                self._log_warning("重连后健康检查失败：心跳发送失败")
                return False
            
            self._log_info("重连后健康检查通过")
            return True
            
        except Exception as e:
            self._log_error(f"重连后健康检查异常: {e}")
            return False
    
    async def _active_health_monitoring(self):
        """🔥 **完全移除重复逻辑**：健康监控100%委托给统一连接池管理器"""
        # 🚨 **彻底修复**：完全移除本地健康监控逻辑，消除重复
        # 所有健康监控功能统一由 unified_connection_pool_manager.py 处理

        self._log_info("🔥 健康监控已100%统一到连接池管理器，本地逻辑已完全移除")

        # 🔥 **统一实现**：仅做注册，不执行任何监控逻辑
        try:
            from websocket.unified_connection_pool_manager import get_connection_pool_manager
            pool_manager = get_connection_pool_manager()

            connection_id = self.pool_connection_id or f"{self.exchange_name.lower()}_websocket"
            await pool_manager.register_for_health_monitoring(connection_id, self)

            self._log_info(f"✅ 已注册到统一健康监控: {connection_id}")

            # 🔥 **关键**：注册后直接返回，不执行任何本地监控逻辑
            return

        except Exception as e:
            self._log_error(f"统一健康监控注册失败: {e}")
            # 🚨 **严格要求**：即使失败也不执行本地监控，避免重复逻辑

    async def _basic_connection_check(self):
        """🔥 **简化连接检查**：仅做基础状态检查"""
        try:
            return self.ws and self.ws.open and self.running
        except Exception:
            return False
    
    
    async def _handle_raw_message(self, message_data):
        """
        处理原始消息 - 🔥 修复版：支持带接收时间戳的消息格式
        :param message_data: 消息数据（可能是字符串或包含接收时间戳的字典）
        """
        try:
            # 🔥 新格式：支持带接收时间戳的消息
            if isinstance(message_data, dict) and "original_message" in message_data:
                message = message_data["original_message"]
                receive_timestamp_ms = message_data["receive_timestamp_ms"]
                
                # 计算队列延迟
                current_time_ms = int(time.time() * 1000)
                queue_delay_ms = current_time_ms - receive_timestamp_ms
                
                if queue_delay_ms > 100:  # 队列延迟超过100ms记录
                    self._log_debug(f"队列延迟: {queue_delay_ms}ms")
                    
            else:
                # 🔥 兼容旧格式：直接的消息字符串
                message = message_data
                receive_timestamp_ms = int(time.time() * 1000)
                queue_delay_ms = 0
            
            # 更新最后消息时间
            self.last_message_time = time.time()
            self.message_count += 1
            
            # 定期记录统计信息
            if time.time() - self.last_stats_time > self.stats_interval:
                # 非异步调用_log_stats，避免产生错误
                self._log_stats()
            
            # 记录原始消息（仅调试级别，只写入日志文件）
            if message and len(message) > 0:
                self._log_debug(f"收到消息: {message[:200]}{'...' if len(message) > 200 else ''}")
            else:
                self._log_warning("收到空消息")
                return
            
            # 解析JSON
            try:
                parsed_data = json.loads(message)
                
                # 🔥 **根源性修复**：将接收时间戳传递给具体的消息处理器
                if receive_timestamp_ms:
                    # 创建包含接收时间戳的数据结构
                    data_with_timestamp = {
                        **parsed_data,  # 原始消息数据
                        "receive_timestamp_ms": receive_timestamp_ms  # WebSocket接收时间戳
                    }
                    await self.handle_message(data_with_timestamp)
                else:
                    await self.handle_message(parsed_data)
                    
            except json.JSONDecodeError:
                # 🔥 **关键修复**：处理非JSON消息（如OKX的pong响应）
                if message.strip() in ["pong", "ping"]:
                    # 对于ping/pong消息，直接传递字符串给handle_message
                    await self.handle_message(message.strip())
                else:
                    self._log_warning(f"无效JSON: {message[:100]}...")
                    self.error_count += 1
                
        except Exception as e:
            self._log_error(f"处理消息异常: {e}", exc_info=True)
            self.error_count += 1
    
    async def _unified_message_distributor(self):
        """🔥 **根源性修复核心**：统一消息分发器 - 解决WebSocket单一消费者原则冲突"""
        self._log_info("🚀 启动统一消息分发器，从根源解决WebSocket并发冲突")
        
        while self.running:
            try:
                # 从消息队列获取消息
                try:
                    message_data = await asyncio.wait_for(self._message_queue.get(), timeout=1.0)
                except asyncio.TimeoutError:
                    continue  # 超时继续循环
                
                message_type, message_content = message_data
                
                if message_type == "websocket_message":
                    # 处理WebSocket消息
                    await self._handle_raw_message(message_content)
                    
                elif message_type == "health_check_request":
                    # 处理健康检查请求
                    await self._handle_health_check_request(message_content)
                    
                elif message_type == "heartbeat_request":
                    # 处理心跳请求
                    await self._handle_heartbeat_request(message_content)
                    
                elif message_type == "connection_status_query":
                    # 处理连接状态查询
                    await self._handle_connection_status_query(message_content)
                    
                else:
                    self._log_warning(f"未知消息类型: {message_type}")
                
                # 标记任务完成
                self._message_queue.task_done()
                
            except asyncio.CancelledError:
                self._log_debug("消息分发器被取消")
                break
            except Exception as e:
                self._log_error(f"消息分发器异常: {e}", exc_info=True)
                await asyncio.sleep(0.1)
    
    async def _enhanced_health_monitoring(self):
        """🔥 **保留功能的增强健康监控**：通过消息队列通信，避免直接WebSocket操作"""
        self._log_info("🔥 启动增强健康监控（保留原有功能，避免并发冲突）")
        
        while self.running and self.auto_recovery_enabled:
            try:
                await asyncio.sleep(self.health_check_interval)
                
                if not self.running:
                    break
                
                # 🔥 修复：移除重复的健康检查逻辑，由enhanced_blocking_tracker统一处理
                # 保持基本的连接状态检查即可
                
                # 🔥 修复：保持基本连接状态检查，但移除消息队列复杂逻辑
                if not await self._safe_connection_check():
                    self._log_debug("连接状态检查失败，等待下次检查")
                
            except asyncio.CancelledError:
                self._log_debug("增强健康监控被取消")
                break
            except Exception as e:
                self._log_error(f"增强健康监控异常: {e}", exc_info=True)
                await asyncio.sleep(1.0)
    
    async def _safe_connection_check(self):
        """安全的连接检查，不直接操作WebSocket"""
        try:
            if not self.ws or not self.ws.open:
                return False
            
            # 🔥 修复：只检查基本WebSocket状态，移除silent_duration检查
            # silent_duration检查由enhanced_blocking_tracker统一处理
            
            return True
        except Exception:
            return False
    
    async def _handle_health_check_request(self, request):
        """处理健康检查请求 - 🔥 修复：简化逻辑，由enhanced_blocking_tracker统一处理"""
        try:
            # 🔥 修复：移除重复的silent_disconnect_check逻辑，由enhanced_blocking_tracker统一处理
            # 只保留基本的连接状态检查
            if request.get('type') == 'connection_status_check':
                self._log_debug("执行基本连接状态检查")
                if not await self._safe_connection_check():
                    await self._queue_reconnect_request()

        except Exception as e:
            self._log_error(f"健康检查处理失败: {e}")
    
    async def _handle_connection_status_query(self, request):
        """处理连接状态查询"""
        try:
            if request['type'] == 'connection_status_check':
                self._log_debug("处理连接状态检查请求")
                
                if not await self._safe_connection_check():
                    await self._queue_reconnect_request()
                    
        except Exception as e:
            self._log_error(f"处理连接状态查询异常: {e}")
    
    async def _queue_reconnect_request(self):
        """队列重连请求"""
        try:
            # 设置重连标记，让主循环处理
            self._reconnect_needed = True
            self._log_info("重连请求已排队，等待主循环处理")
        except Exception as e:
            self._log_error(f"排队重连请求失败: {e}")
    
    async def _handle_heartbeat_request(self, request):
        """处理心跳请求"""
        try:
            # 心跳请求由主循环的WebSocket操作锁保护的发送逻辑处理
            self._log_debug("心跳请求已接收，等待主循环处理")
        except Exception as e:
            self._log_error(f"处理心跳请求异常: {e}")
    
    async def _heartbeat_loop(self):
        """🔥 **增强心跳循环**：通过消息队列协调，避免并发WebSocket操作"""
        self._log_debug("启动增强心跳循环")
        retry_count = 0
        max_heartbeat_failures = 5  # 心跳失败容错
        
        # 🚨 关键修复：初始化WebSocket操作锁，确保单一消费者原则
        if not hasattr(self, '_ws_operation_lock'):
            self._ws_operation_lock = asyncio.Lock()
        
        while self.running:
            try:
                now = time.time()
                
                # 🔥 修复：通过消息队列协调心跳，避免与主循环的recv()冲突
                if now - self.last_message_time > self.heartbeat_interval:
                    # 🚨 关键修复：使用锁保护WebSocket操作，遵守单一消费者原则
                    try:
                        async with self._ws_operation_lock:
                            if self.ws and self.ws.open:
                                success = await self.send_heartbeat()
                                if success:
                                    self._log_debug("心跳发送成功")
                                    self.last_message_time = time.time()
                                    retry_count = 0
                                else:
                                    self._log_warning("心跳发送失败，可能连接已断开")
                                    retry_count += 1
                            else:
                                self._log_debug("WebSocket未连接，跳过心跳")
                                retry_count += 1
                    except Exception as e:
                        self._log_warning(f"心跳发送异常: {e}")
                        retry_count += 1
                    
                    # 心跳失败处理
                    if retry_count >= max_heartbeat_failures:
                        self._log_warning(f"心跳连续失败{max_heartbeat_failures}次，请求重连")
                        # 🚨 关键修复：通过消息队列请求重连，避免并发操作
                        await self._queue_reconnect_request()
                        retry_count = 0
                
                # 🔥 优化：降低心跳任务的CPU占用
                check_interval = min(1.0, self.heartbeat_interval / 10)
                await asyncio.sleep(check_interval)
                
            except asyncio.CancelledError:
                self._log_debug("心跳循环被取消")
                break
            except Exception as e:
                self._log_error(f"心跳循环异常: {e}", exc_info=True)
                await asyncio.sleep(1.0)
        
        # 🚨 关键修复：初始化WebSocket操作锁，确保单一消费者原则
        if not hasattr(self, '_ws_operation_lock'):
            self._ws_operation_lock = asyncio.Lock()
        
        while self.running:
            try:
                now = time.time()
                
                # 🔥 修复：只做状态检查，避免与主消息循环的recv()冲突
                if now - self.last_message_time > self.heartbeat_interval:
                    # 🚨 关键修复：使用锁保护WebSocket操作，遵守单一消费者原则
                    try:
                        async with self._ws_operation_lock:
                            if self.ws and self.ws.open:
                                success = await self.send_heartbeat()
                                if success:
                                    self._log_debug("心跳发送成功")
                                    self.last_message_time = time.time()
                                    retry_count = 0
                                else:
                                    self._log_warning("心跳发送失败，可能连接已断开")
                                    retry_count += 1
                            else:
                                self._log_debug("WebSocket未连接，跳过心跳")
                                retry_count += 1
                    except Exception as e:
                        self._log_warning(f"心跳发送异常: {e}")
                        retry_count += 1
                    
                    # 心跳失败处理
                    if retry_count >= max_heartbeat_failures:
                        self._log_warning(f"心跳连续失败{max_heartbeat_failures}次，标记需要重连")
                        # 🚨 关键修复：设置标记让主循环处理重连，避免并发操作
                        self._heartbeat_reconnect_needed = True
                        retry_count = 0
                
                # 🔥 优化：降低心跳任务的CPU占用
                check_interval = min(1.0, self.heartbeat_interval / 10)
                await asyncio.sleep(check_interval)
                
            except asyncio.CancelledError:
                self._log_debug("心跳循环被取消")
                break
            except Exception as e:
                self._log_error(f"心跳循环异常: {e}", exc_info=True)
                await asyncio.sleep(1.0)
    
    async def run(self):
        """运行WebSocket客户端"""
        self.running = True
        self.reconnect_count = 0
        self.message_count = 0
        self.error_count = 0
        self.last_stats_time = time.time()
        self.last_message_time = time.time()  # 初始化为当前时间
        
        # 首次连接
        connected = await self._connect()
        if not connected:
            connected = await self._reconnect()
            if not connected:
                self._log_error(f"无法建立WebSocket连接，退出")
                self.running = False
                return
        
        # 启动心跳
        if self.heartbeat_task and not self.heartbeat_task.done():
            self.heartbeat_task.cancel()
            try:
                await self.heartbeat_task
            except asyncio.CancelledError:
                pass
        
        self.heartbeat_task = asyncio.create_task(self._heartbeat_loop())
        self.heartbeat_task.set_name(f"heartbeat_{self.exchange_name}")
        
        # 🔥 **根源性修复**：保留健康监控功能，通过消息队列机制解决并发冲突
        # 而不是简单禁用功能，这违背了"保留现有功能"的核心要求
        
        # 🚨 **CRITICAL修复**：实现WebSocket消息队列，从根源解决并发访问问题
        if not hasattr(self, '_message_queue'):
            self._message_queue = asyncio.Queue()
        if not hasattr(self, '_message_distributor_task'):
            self._message_distributor_task = None
        
        # 🔥 启动统一消息分发器，确保单一消费者原则
        self._message_distributor_task = asyncio.create_task(self._unified_message_distributor())
        self._message_distributor_task.set_name(f"message_distributor_{self.exchange_name}")
        
        # 🔥 保留健康监控功能 - 通过消息队列通信，避免直接WebSocket操作
        if self.auto_recovery_enabled:
            self.health_monitor_task = asyncio.create_task(self._enhanced_health_monitoring())
            self.health_monitor_task.set_name(f"health_monitor_{self.exchange_name}")
            self._log_info("🔥 健康监控功能已保留，通过消息队列机制避免并发冲突")
        else:
            self.health_monitor_task = None
            self._log_info(f"健康监控已禁用（配置设置）")



        # 🚨 **CRITICAL修复**：初始化WebSocket操作锁，确保单一消费者原则
        if not hasattr(self, '_ws_operation_lock'):
            self._ws_operation_lock = asyncio.Lock()
        
        # 🚨 初始化心跳重连标记
        self._heartbeat_reconnect_needed = False

        try:
            # 🔥 **根源性修复核心**：主循环只负责接收WebSocket消息，分发给消息队列
            # 这是解决WebSocket单一消费者原则的根本方案
            while self.running and self.ws:
                try:
                    # 🚨 检查重连请求
                    if hasattr(self, '_reconnect_needed') and self._reconnect_needed:
                        self._log_info("响应队列重连请求")
                        self._reconnect_needed = False
                        if not await self._reconnect():
                            break
                        continue
                    
                    # 🚨 **CRITICAL**：主循环是唯一调用recv()的地方，确保单一消费者原则
                    timeout = max(30, self.heartbeat_interval * 1.5)
                    
                    # 🔥 使用锁确保recv()操作的独占性
                    async with self._ws_operation_lock:
                        if self.ws and self.ws.open:
                            message = await asyncio.wait_for(self.ws.recv(), timeout=timeout)
                        else:
                            self._log_warning("WebSocket连接已断开")
                            break
                    
                    # 🔥 **根源性修复**：消息入队时立即标记接收时间，避免队列延迟导致时间戳老化
                    # 这解决了数据老化30-60秒的根本问题：原始时间戳在队列中等待被处理
                    message_with_receive_time = {
                        "original_message": message,
                        "receive_timestamp_ms": int(time.time() * 1000)  # 立即标记接收时间
                    }
                    await self._message_queue.put(("websocket_message", message_with_receive_time))
                    
                    # 更新最后消息时间
                    self.last_message_time = time.time()
                    
                except asyncio.TimeoutError:
                    self._log_debug(f"接收消息超时 ({timeout}秒)，检查连接状态")
                    # 检查是否需要重连
                    now = time.time()
                    if now - self.last_message_time > self.heartbeat_interval * 2:  # 两个心跳周期没有消息，尝试重连
                        self._log_warning(f"{int(now - self.last_message_time)}秒未收到消息，尝试重连")
                        if not await self._reconnect():
                            self.running = False
                            
                except websockets.exceptions.ConnectionClosed as e:
                    self._log_warning(f"WebSocket连接关闭: {e}")
                    
                    if not self.running:
                        break
                    
                    # 重连
                    connected = await self._reconnect()
                    if not connected:
                        break
                        
                except Exception as e:
                    self._log_error(f"WebSocket接收消息异常: {e}", exc_info=True)
                    
                    if not self.running:
                        break
                    
                    # 重连
                    connected = await self._reconnect()
                    if not connected:
                        break
            
        finally:
            # 清理
            self.running = False
            
            # 取消心跳任务
            if self.heartbeat_task and not self.heartbeat_task.done():
                self.heartbeat_task.cancel()
                try:
                    await self.heartbeat_task
                except asyncio.CancelledError:
                    pass
            
            # 🔥 完美修复：取消主动健康监控任务
            if hasattr(self, 'health_monitor_task') and self.health_monitor_task and not self.health_monitor_task.done():
                self.health_monitor_task.cancel()
                try:
                    await self.health_monitor_task
                except asyncio.CancelledError:
                    pass
            
            # 关闭连接
            if self.ws:
                await self.ws.close()
                self.ws = None
                
            # 最终统计
            self._log_stats()
            self._log_info(f"WebSocket客户端已关闭")
    
    async def close(self):
        """关闭WebSocket客户端"""
        self.running = False
        
        # 取消心跳任务
        if hasattr(self, 'heartbeat_task') and self.heartbeat_task and not self.heartbeat_task.done():
            self.heartbeat_task.cancel()
            try:
                await self.heartbeat_task
            except asyncio.CancelledError:
                pass
        
        # 🔥 根源性修复：取消统一消息分发器任务
        if hasattr(self, '_message_distributor_task') and self._message_distributor_task and not self._message_distributor_task.done():
            self._message_distributor_task.cancel()
            try:
                await self._message_distributor_task
            except asyncio.CancelledError:
                pass
        
        # 🔥 完美修复：取消主动健康监控任务
        if hasattr(self, 'health_monitor_task') and self.health_monitor_task and not self.health_monitor_task.done():
            self.health_monitor_task.cancel()
            try:
                await self.health_monitor_task
            except asyncio.CancelledError:
                pass
        
        # 关闭连接 - 🔥 修复：增强空指针保护
        if self.ws is not None:
            try:
                await self.ws.close()
                self._log_debug("WebSocket连接已正常关闭")
            except Exception as e:
                self._log_warning(f"关闭WebSocket连接时出错: {e}")
            finally:
                self.ws = None
        else:
            self._log_debug("WebSocket连接已经为空，无需关闭")
            
        self._log_info(f"WebSocket客户端关闭中...")

    async def _check_connection(self):
        """检查WebSocket连接状态 - 🔥 完美修复版：增强连接检查逻辑"""
        if not self.ws or not self.ws.open:
            self._log_warning("WebSocket连接已关闭")
            return False
        
        # 🔥 修复：移除重复的silent_duration检查，由enhanced_blocking_tracker统一处理
        # 只保留基本的WebSocket连接状态检查
        
        # 🔥 完美修复：增加WebSocket状态详细检查
        try:
            # 检查WebSocket对象的内部状态
            if hasattr(self.ws, 'state'):
                from websockets.protocol import State
                if self.ws.state != State.OPEN:
                    self._log_warning(f"WebSocket状态异常: {self.ws.state}")
                    return False
            
            # 尝试发送ping来测试连接
            if hasattr(self.ws, 'ping'):
                try:
                    await asyncio.wait_for(self.ws.ping(), timeout=5.0)
                    return True
                except asyncio.TimeoutError:
                    self._log_warning("Ping超时，连接可能异常")
                    return False
                except Exception as e:
                    self._log_warning(f"Ping失败: {e}")
                    return False
        except Exception as e:
            self._log_warning(f"连接状态检查异常: {e}")
            return False
            
        return True
    
    def is_connected(self) -> bool:
        """
        检查WebSocket是否已连接
        
        Returns:
            bool: 连接状态
        """
        try:
            if not self.ws:
                return False
            
            if not hasattr(self.ws, 'open'):
                return False
                
            return self.ws.open and self.running
        except Exception as e:
            self._log_debug(f"检查连接状态时出错: {e}")
            return False
    
    async def start_connection(self):
        """启动WebSocket连接"""
        if self.running:
            self._log_warning("WebSocket已在运行中")
            return True
            
        try:
            self._log_info("启动WebSocket连接...")
            # 创建连接任务但不等待，让它在后台运行
            self.connection_task = asyncio.create_task(self.run())
            
            # 等待更长时间，让连接建立 - 从0.5秒增加到3秒
            max_wait_time = 3.0
            check_interval = 0.1
            waited_time = 0
            
            while waited_time < max_wait_time:
                await asyncio.sleep(check_interval)
                waited_time += check_interval
                
                # 检查连接状态
                if self.is_connected():
                    self._log_info(f"WebSocket连接已建立，耗时: {waited_time:.1f}秒")
                    return True
                
                # 如果连接任务已完成但连接失败，提前退出
                if self.connection_task.done():
                    break
            
            # 超时或连接失败
            self._log_warning(f"WebSocket连接建立失败，等待时间: {waited_time:.1f}秒")
            return False
                
        except Exception as e:
            self._log_error(f"启动WebSocket连接失败: {e}")
            return False

    async def stop(self):
        """停止WebSocket连接"""
        try:
            self._log_info("正在停止WebSocket连接...")
            self.running = False
            
            # 取消所有任务
            if self.connection_task and not self.connection_task.done():
                self.connection_task.cancel()
                try:
                    await self.connection_task
                except asyncio.CancelledError:
                    pass
            
            if self.heartbeat_task and not self.heartbeat_task.done():
                self.heartbeat_task.cancel()
                try:
                    await self.heartbeat_task
                except asyncio.CancelledError:
                    pass
            
            if self.monitor_task and not self.monitor_task.done():
                self.monitor_task.cancel()
                try:
                    await self.monitor_task
                except asyncio.CancelledError:
                    pass
            
            # 关闭WebSocket连接
            if self.ws:
                try:
                    await self.ws.close()
                except Exception:
                    pass
                self.ws = None
            
            self._log_info("WebSocket连接已停止")
            return True
            
        except Exception as e:
            self._log_error(f"停止WebSocket连接失败: {e}")
            return False

