"""
Bybit WebSocket客户端
支持现货和期货的深度数据
"""

import asyncio
import json
import time
import websockets
import logging
import os
import sys
from typing import Dict, Any, List, Optional, Callable
from datetime import datetime

# 尝试导入基类和自定义日志系统
try:
    from websocket.ws_client import WebSocketClient
    # 确保使用正确的日志器
    logger = logging.getLogger("websocket.bybit")
except ImportError:
    # 开发环境导入路径
    sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    from websocket.ws_client import WebSocketClient
    # 确保使用正确的日志器
    logger = logging.getLogger("websocket.bybit")


class BybitWebSocketClient(WebSocketClient):
    """Bybit WebSocket客户端"""
    
    def __init__(self, market_type="spot", settings=None):
        """
        初始化Bybit WebSocket客户端
        Args:
            market_type: 市场类型，可选值: "spot", "futures"
            settings: 全局配置，可选
        """
        super().__init__("BYBIT", settings)
        self.market_type = market_type.lower()
        self.symbols = []
        
        # 🔥 时间同步机制 - 使用统一时间戳处理器
        from websocket.unified_timestamp_processor import get_timestamp_processor
        self.timestamp_processor = get_timestamp_processor("bybit")

        # 🔥 **100%完美修复**：集成统一连接池管理器
        self._integrated_with_pool = False
        self.pool_connection_id = None
        try:
            from websocket.unified_connection_pool_manager import get_connection_pool_manager
            self.pool_manager = get_connection_pool_manager()
            self._integrated_with_pool = True
            self.pool_connection_id = f"bybit_{market_type}_websocket"
            self._log_info("✅ Bybit已集成统一连接池管理器")
        except Exception as e:
            self._log_warning(f"⚠️ Bybit统一连接池管理器集成失败: {e}")

        # 保留这些属性以兼容现有代码
        self.time_offset = 0  # 时间偏移（毫秒）
        self.time_synced = False
        self.last_sync_time = 0
        
        # 🔥 修复：移除重复的数据流监控，由enhanced_blocking_tracker统一处理
        # self.last_data_time = 0
        # self.data_flow_timeout = 30  # 30秒无数据认为阻塞

        # 🔥 **API符合性修复**：添加Bybit API限速控制
        self.rate_limiter = {
            'max_requests_per_second': 10,  # Bybit WebSocket限制
            'request_timestamps': [],
            'last_cleanup': time.time()
        }

        # 设置不同市场的WebSocket URL
        if self.market_type == "spot":
            self.ws_url = "wss://stream.bybit.com/v5/public/spot"
            self._log_info("初始化Bybit现货WebSocket")
        elif self.market_type == "futures" or self.market_type == "linear":
            self.ws_url = "wss://stream.bybit.com/v5/public/linear"
            self._log_info("初始化Bybit永续合约WebSocket")
        else:
            self._log_error(f"不支持的市场类型: {market_type}")
            raise ValueError(f"不支持的市场类型: {market_type}")
        
        # 订阅数据和处理函数的映射
        self.handlers = {
            "orderbook": self._handle_orderbook,
            "trade": self._handle_trade,
            # ticker处理已移除
            "kline": self._handle_kline
        }

        # 🔥 新增：维护完整的订单簿状态（修复增量更新问题）
        self.orderbook_states = {}  # {symbol: {"asks": {price: quantity}, "bids": {price: quantity}}}
        self.orderbook_locks = {}   # {symbol: asyncio.Lock()}
        
        # 🔥 统一修复：集成增强的阻塞追踪器
        try:
            from websocket.enhanced_blocking_tracker import log_websocket_data_received, log_websocket_subscription_attempt
            self._log_data_received = log_websocket_data_received
            self._log_subscription_attempt = log_websocket_subscription_attempt
        except ImportError:
            self._log_data_received = lambda *args, **kwargs: None
            self._log_subscription_attempt = lambda *args, **kwargs: None

        # 初始化消息统计
        self.message_stats = {
            "orderbook": 0,
            "publicTrade": 0,
            # ticker统计已移除
            "kline": 0,
            "other": 0
        }
        
        # 初始化其他属性  
        self.active_subscriptions = set()
        self.connection_timeout = 10  # 🔥 修复：连接超时统一为10秒，与Gate.io、OKX保持一致
        self.connect_warning_threshold = 1.2  # 为Bybit设置适当的连接时间警告阈值
        self.last_stats_time = time.time()
        # 修复：从settings读取统计间隔，而不是硬编码
        if settings and hasattr(settings, 'system') and hasattr(settings.system, 'ws_stats_interval'):
            self.stats_interval = settings.system.ws_stats_interval
        else:
            self.stats_interval = 60

    def set_symbols(self, symbols: List[str]):
        """
        设置交易对 - 🔧 集成统一交易对验证机制，解决SHIB期货合约不存在问题
        
        Args:
            symbols: 交易对列表，例如 ["BTCUSDT", "ETHUSDT"]
        """
        # 🔧 **关键修复**：集成统一交易对验证机制，动态检测和过滤
        try:
            from core.unified_symbol_validator import get_symbol_validator
            validator = get_symbol_validator()
            
            # 🔧 智能过滤：解决SHIB等不支持期货合约的问题
            supported_symbols = validator.filter_supported_symbols(symbols, "bybit", self.market_type)
            
            if not supported_symbols:
                self._log_warning("⚠️ 经过Bybit验证，没有支持的交易对，使用默认交易对")
                if self.market_type == "futures":
                    supported_symbols = ["BTCUSDT", "ETHUSDT"]  # 期货兜底
                else:
                    supported_symbols = ["BTCUSDT", "ETHUSDT", "ADAUSDT"]  # 现货兜底
            
            self.symbols = supported_symbols
            self._log_info(f"✅ Bybit {self.market_type} 设置验证后的交易对: {len(supported_symbols)}个 - {', '.join(supported_symbols)}")
            self._log_info(f"🔧 已自动过滤SHIB等不支持期货合约的交易对")
            
        except ImportError:
            # 🔧 兜底处理：验证器不可用时使用原逻辑，但仍过滤SHIB
            formatted_symbols = []
            
            for symbol in symbols:
                # 统一格式为大写，并移除可能的分隔符
                s = symbol.replace("-", "").replace("_", "").upper()
                
                # 🔧 **特殊修复**：手动过滤已知不支持的交易对
                if self.market_type == "futures" and "SHIB" in s:
                    self._log_debug(f"🔧 手动过滤Bybit期货不支持的交易对: {s}")
                    continue
                
                formatted_symbols.append(s)
            
            self.symbols = formatted_symbols
            self._log_info(f"✅ Bybit {self.market_type} 设置交易对: {len(formatted_symbols)}个 - {', '.join(formatted_symbols)}")
            
        except Exception as e:
            self._log_error(f"交易对验证失败: {e}，使用基础过滤逻辑")
            formatted_symbols = []
            for symbol in symbols:
                s = symbol.replace("-", "").replace("_", "").upper()
                # 至少过滤SHIB期货
                if not (self.market_type == "futures" and "SHIB" in s):
                    formatted_symbols.append(s)
            self.symbols = formatted_symbols

    async def _sync_time(self):
        """🔥 已删除：使用统一时间戳处理器替代"""
        # 这个方法已被统一时间戳处理器替代，保留空实现以兼容
        pass



    async def run(self):
        """运行WebSocket客户端 - 🔥 修复：移除独立时间同步，使用集中式同步"""
        # 🔥 关键修复：移除WebSocket客户端的独立时间同步调用
        # 时间同步现在由系统启动时的集中式同步管理，避免并发冲突

        # 🔥 修复：使用实例属性访问时间戳处理器
        # 仅检查同步状态，不执行同步操作
        if self.timestamp_processor.time_synced:
            self._log_info(f"✅ Bybit时间已同步，偏移量: {self.timestamp_processor.time_offset}ms")
        else:
            self._log_warning(f"⚠️ Bybit时间未同步，将使用统一时间基准")

        # 调用父类的run方法
        await super().run()

    def get_ws_url(self) -> str:
        """获取WebSocket URL"""
        return self.ws_url
    
    async def subscribe_channels(self):
        """
        订阅频道 - 🔥 修复版本：使用分批订阅，避免"args size >10"错误
        
        Returns:
            bool: 订阅是否成功
        """
        if not self.symbols:
            self._log_error("未设置交易对，无法订阅")
            return False
        
        try:
            # 清理现有订阅
            self.active_subscriptions.clear()
            
            self._log_info(f"🚀 开始订阅Bybit {self.market_type} WebSocket，交易对数量: {len(self.symbols)}")
            
            # 🔥 关键修复：使用分批订阅方法，避免一次性发送过多频道导致"args size >10"错误
            
            # 🚀 极速优化：并行订阅，删除所有等待时间
            self._log_info("🚀 并行订阅所有数据...")

            # 🚀 官方SDK要求：按照官方示例进行并行订阅优化
            # 参考官方SDK: pybit/examples/websocket_example_quickstart.py

            # 🔥 **CRITICAL修复**：采用顺序订阅，避免并发冲突
            # 修复：移除asyncio.gather，使用顺序执行确保WebSocket连接稳定

            try:
                # 顺序订阅orderbook数据
                result = await self._subscribe_orderbook()
                if isinstance(result, Exception):
                    self._log_error(f"orderbook订阅失败: {result}")
                    subscription_success = False
                elif not result:
                    self._log_error("orderbook订阅失败")
                    subscription_success = False
                else:
                    self._log_info("✅ orderbook订阅成功")

            except Exception as e:
                self._log_error(f"订阅任务失败: {e}")
                subscription_success = False
            
            # 计算总订阅数量
            expected_subscriptions = len(self.symbols) * 1  # 每个symbol订阅1个频道(orderbook)
            self._log_info(f"✅ Bybit {self.market_type} 订阅完成，预期订阅数: {expected_subscriptions}")
            
            return True
            
        except Exception as e:
            self._log_error(f"❌ Bybit订阅频道失败: {e}", exc_info=True)
            return False
    
    async def handle_message(self, message: Dict[str, Any]):
        """
        处理WebSocket消息 - 防刷屏优化
        
        Args:
            message: JSON解析后的消息对象
        """
        try:
            # 处理ping-pong消息
            if "op" in message and message["op"] == "ping":
                await self.send({"op": "pong"})
                self._log_debug("收到ping，已回复pong")
                return
            
            # 处理订阅响应
            if "op" in message and message["op"] == "subscribe" and "success" in message:
                if message["success"]:
                    self._log_info(f"成功订阅: {message.get('ret_msg', '')}")
                else:
                    # 🔥 **通用系统修复**：智能处理订阅失败，不影响整体运行
                    ret_msg = message.get('ret_msg', '')
                    
                    # 🔥 **通用系统支持任意代币修复**：基于Bybit官方API规范的智能过滤
                    if ("not found" in ret_msg.lower()) or \
                       ("invalid symbol" in ret_msg.lower()) or \
                       ("symbol does not exist" in ret_msg.lower()) or \
                       ("unsupported symbol" in ret_msg.lower()) or \
                       ("unknown" in ret_msg.lower()) or \
                       ("unsupported" in ret_msg.lower()) or \
                       ("invalid topic" in ret_msg.lower()):
                        # 交易对不存在 - 这是正常的，通用系统应自动过滤
                        self._log_debug(f"🔧 [BYBIT-{self.market_type.upper()}] 自动过滤不支持的交易对: {ret_msg}")
                        # 不记录为错误，这是通用系统的智能适应
                        return
                    else:
                        # 其他错误才记录为系统问题
                        self._log_warning(f"⚠️ [BYBIT-{self.market_type.upper()}] 订阅失败: {ret_msg}")
                        # 🔥 记录真正的订阅失败日志
                        from .websocket_logger import log_websocket_subscription_failure
                        log_websocket_subscription_failure("error", f"订阅响应失败",
                                                         exchange="bybit",
                                                         market_type=self.market_type,
                                                         error_message=ret_msg)
                return
            
            # 处理数据更新
            if "topic" in message and "data" in message:
                topic = message["topic"]
                data = message["data"]

                # 🔥 关键修复：获取消息类型，正确处理snapshot和delta
                message_type = message.get("type", "delta")  # 默认为delta保持向后兼容

                # 🔥 修复：移除过度的频率限制，这可能导致数据丢失
                # 原来的500ms限制太严格，可能导致重要数据被跳过
                # if "orderbook" in topic:
                #     current_time = time.time()
                #     if hasattr(self, '_last_orderbook_time'):
                #         if current_time - self._last_orderbook_time < 0.5:  # 500ms限制
                #             return
                #     self._last_orderbook_time = current_time

                # 提取频道类型和交易对
                parts = topic.split(".")
                if len(parts) < 2:
                    self._log_warning(f"无效的topic格式: {topic}")
                    return

                channel_type = parts[0]
                symbol = parts[-1] if len(parts) >= 2 else None

                # 🔥 重要：确保symbol不为空
                if not symbol:
                    self._log_warning(f"Bybit topic无法提取symbol: {topic}")
                    return

                # 🔥 添加详细的topic解析日志，包含消息类型
                self._log_info(f"🔍 Bybit处理消息: topic={topic}, channel={channel_type}, symbol={symbol}, type={message_type}")

                # 更新统计信息
                # 🔥 修复：移除重复的数据流时间更新，由enhanced_blocking_tracker统一处理
                # self.last_data_time = time.time()
                
                # 🔥 统一修复：记录数据接收到阻塞追踪器
                self._log_data_received("bybit", self.market_type, symbol, data)
                
                # 🔥 **新增：统一数据流监控**，确保与时间戳处理器协调
                try:
                    # 更新时间戳处理器的数据时间，避免错误的阻塞检测
                    current_timestamp = int(time.time() * 1000)
                    self.timestamp_processor._last_valid_timestamp = current_timestamp
                    self.timestamp_processor._last_update_time = current_timestamp
                except Exception as e:
                    self._log_debug(f"更新时间戳处理器失败: {e}")
                
                self._update_stats(channel_type)

                # 调用相应的处理函数
                if channel_type == "orderbook":
                    await self._handle_orderbook(symbol, data, message_type)
                elif channel_type == "publicTrade":
                    await self._handle_trade(symbol, data)
                elif channel_type == "tickers":
                    # ticker处理已移除，跳过
                    self._log_debug(f"跳过ticker数据: {symbol}")
                elif channel_type == "kline":
                    await self._handle_kline(symbol, data)
                else:
                    # 🔥 记录未处理的频道，可能发现问题
                    self._log_warning(f"⚠️ Bybit未处理的频道: {channel_type} in {topic}")
            
        except Exception as e:
            self._log_error(f"处理消息出错: {str(e)}", exc_info=True)
    
    async def _handle_orderbook(self, symbol, data, message_type="delta"):
        """处理订单簿数据 - 🔥 修复版本：正确处理snapshot和delta消息"""
        try:
            import asyncio
            # 🔥 新增：使用统一订单簿验证器
            from websocket.orderbook_validator import get_orderbook_validator
            # 🔥 新增：使用WebSocket性能监控器
            from websocket.performance_monitor import record_message_latency

            start_time = time.time()

            # 🔥 关键修复：维护完整的订单簿状态
            if symbol not in self.orderbook_states:
                self.orderbook_states[symbol] = {
                    "asks": {},  # {price: quantity}
                    "bids": {},  # {price: quantity}
                    "last_update": 0
                }

            if symbol not in self.orderbook_locks:
                self.orderbook_locks[symbol] = asyncio.Lock()

            # 使用锁防止并发更新
            async with self.orderbook_locks[symbol]:
                state = self.orderbook_states[symbol]

                # 🔥 关键修复：Bybit订单簿数据字段映射修复
                asks = []
                bids = []

                # 🔥 支持多种字段格式，确保数据提取成功
                if "a" in data and "b" in data:
                    # 格式1: {"a": [[price, size]], "b": [[price, size]]}
                    asks = data.get("a", [])
                    bids = data.get("b", [])
                elif "asks" in data and "bids" in data:
                    # 格式2: {"asks": [[price, size]], "bids": [[price, size]]}
                    asks = data.get("asks", [])
                    bids = data.get("bids", [])
                elif isinstance(data, dict) and len(data) == 2:
                    # 格式3: 直接包含两个数组的字典
                    keys = list(data.keys())
                    if len(keys) == 2:
                        asks = data.get(keys[0], [])
                        bids = data.get(keys[1], [])

                # 🔥 新增：处理Bybit V5 API的数据格式
                if not asks and not bids and isinstance(data, list) and len(data) > 0:
                    # 格式4: Bybit V5可能返回数组格式
                    for item in data:
                        if isinstance(item, dict):
                            if "a" in item and "b" in item:
                                asks = item.get("a", [])
                                bids = item.get("b", [])
                                break

                # 🔥 关键修复：根据消息类型正确处理订单簿数据
                if message_type == "snapshot":
                    # 🔥 快照消息：完全替换订单簿状态
                    self._log_info(f"📸 Bybit快照消息: {symbol} - 完全重建订单簿")

                    # 清空现有状态
                    state["asks"].clear()
                    state["bids"].clear()

                    # 重建asks - 🔥 使用高精度Decimal处理
                    for ask in asks:
                        if len(ask) >= 2:
                            from decimal import Decimal
                            price = Decimal(str(ask[0]))
                            quantity = Decimal(str(ask[1]))
                            # 🔥 标准验证：价格和数量都必须大于0
                            if price > 0 and quantity > 0:  # 快照中不应该有0数量
                                # 🔥 使用Decimal作为key，保持高精度
                                state["asks"][price] = quantity

                    # 重建bids - 🔥 使用高精度Decimal处理
                    for bid in bids:
                        if len(bid) >= 2:
                            from decimal import Decimal
                            price = Decimal(str(bid[0]))
                            quantity = Decimal(str(bid[1]))
                            # 🔥 标准验证：价格和数量都必须大于0
                            if price > 0 and quantity > 0:  # 快照中不应该有0数量
                                # 🔥 使用Decimal作为key，保持高精度
                                state["bids"][price] = quantity

                    self._log_info(f"✅ Bybit快照重建完成: {symbol} - asks={len(state['asks'])}, bids={len(state['bids'])}")

                else:
                    # 🔥 增量消息：基于现有状态进行更新
                    self._log_debug(f"🔄 Bybit增量消息: {symbol} - 更新订单簿")

                    # 更新asks - 🔥 使用高精度Decimal处理
                    for ask in asks:
                        if len(ask) >= 2:
                            from decimal import Decimal
                            price = Decimal(str(ask[0]))
                            quantity = Decimal(str(ask[1]))
                            if quantity == 0:
                                # 数量为0表示删除该价格档位
                                state["asks"].pop(price, None)
                            else:
                                # 更新或添加价格档位 - 🔥 使用Decimal保持精度
                                state["asks"][price] = quantity

                    # 更新bids - 🔥 使用高精度Decimal处理
                    for bid in bids:
                        if len(bid) >= 2:
                            from decimal import Decimal
                            price = Decimal(str(bid[0]))
                            quantity = Decimal(str(bid[1]))
                            if quantity == 0:
                                # 数量为0表示删除该价格档位
                                state["bids"].pop(price, None)
                            else:
                                # 更新或添加价格档位 - 🔥 使用Decimal保持精度
                                state["bids"][price] = quantity

                # 🔥 修复：生成完整的前30档订单簿
                # 排序asks (价格从低到高)
                sorted_asks = sorted(state["asks"].items())[:30]  # 🔥 修复：升级为30档深度
                # 排序bids (价格从高到低)
                sorted_bids = sorted(state["bids"].items(), reverse=True)[:30]  # 🔥 修复：升级为30档深度

                # 转换为标准格式
                formatted_asks = [[price, quantity] for price, quantity in sorted_asks]
                formatted_bids = [[price, quantity] for price, quantity in sorted_bids]

                # 🔥 新增：使用统一订单簿验证器
                validator = get_orderbook_validator()
                orderbook_data = {
                    'asks': formatted_asks,
                    'bids': formatted_bids
                }
                validation_result = validator.validate_orderbook_data(
                    orderbook_data,
                    exchange="bybit",
                    symbol=symbol,
                    market_type=self.market_type
                )

                if not validation_result.is_valid:
                    self._log_warning(f"⚠️ Bybit订单簿验证失败: {validation_result.error_message}")
                    return

                # 🔥 关键修复：统一Symbol格式回传 - 使用currency_adapter标准化
                from exchanges.currency_adapter import normalize_symbol
                standard_symbol = normalize_symbol(symbol)  # BTCUSDT → BTC-USDT

                # 🔥 使用统一格式化器创建订单簿数据
                from websocket.unified_data_formatter import get_orderbook_formatter

                formatter = get_orderbook_formatter()
                # 🔥 **关键修复**：使用实例方法调用，启用数据新鲜度检查
                timestamp = self.timestamp_processor.get_synced_timestamp(data)
                
                orderbook_data = formatter.format_orderbook_data(
                    asks=formatted_asks,
                    bids=formatted_bids,
                    symbol=standard_symbol,
                    exchange="bybit",
                    market_type=self.market_type,
                    timestamp=timestamp
                )

                # 🔥 记录性能指标
                record_message_latency(start_time)

                # 🔥 统一数据流：发送market_data事件
                self.emit("market_data", orderbook_data)
            
        except Exception as e:
            # 🚨 订单簿处理失败直接报错，不使用缓存数据
            self._log_error(f"🚨 Bybit订单簿处理失败: {symbol} - {str(e)}")
            # 重新抛出异常，确保上层知道数据处理失败
            raise
    
    async def _handle_trade(self, symbol, data):
        """
        处理成交数据
        
        Args:
            symbol: 交易对
            data: 成交数据
        """
        try:
            if not isinstance(data, list) or not data:
                self._log_warning(f"无效的成交数据格式: {type(data)}")
                return
            
            trades = []
            for trade in data:
                # 提取成交数据 - 🔥 使用高精度Decimal处理
                from decimal import Decimal
                price = Decimal(str(trade.get("p", 0)))
                amount = Decimal(str(trade.get("v", 0)))
                timestamp = trade.get("T", int(time.time() * 1000))
                trade_id = trade.get("i", "")
                side = trade.get("S", "").lower()  # "Buy" or "Sell"
                
                trades.append({
                    "price": float(price),  # 🔥 转换为float用于兼容性，但保持了Decimal的精度
                    "amount": float(amount),  # 🔥 转换为float用于兼容性，但保持了Decimal的精度
                    "timestamp": timestamp,
                    "id": trade_id,
                    "side": side
                })
            
            # 创建标准化的成交数据
            trade_data = {
                "exchange": "BYBIT",
                "symbol": symbol,
                "timestamp": int(time.time() * 1000),
                "trades": trades,
                "market_type": self.market_type
            }
            
            # 记录debug日志
            self._log_debug(f"成交 {symbol}: {len(trades)}笔")
            
            # 触发回调
            self.emit("trade", trade_data)
            
        except Exception as e:
            self._log_error(f"处理成交数据出错: {str(e)}", exc_info=True)
    
    # 🔥 TICKER处理方法已完全删除 - 系统只使用OrderBook数据
    # 🔥 TICKER处理方法已完全删除 - 系统只使用OrderBook数据
    
    async def _handle_kline(self, symbol, data):
        """
        处理K线数据
        
        Args:
            symbol: 交易对
            data: K线数据
        """
        try:
            if not isinstance(data, list) or not data:
                self._log_warning(f"无效的K线数据格式: {type(data)}")
                return
            
            klines = []
            for kline in data:
                # 提取K线数据 - 🔥 使用高精度Decimal处理
                from decimal import Decimal
                timestamp = kline.get("start", 0)
                open_price = Decimal(str(kline.get("open", 0)))
                high_price = Decimal(str(kline.get("high", 0)))
                low_price = Decimal(str(kline.get("low", 0)))
                close_price = Decimal(str(kline.get("close", 0)))
                volume = Decimal(str(kline.get("volume", 0)))

                klines.append({
                    "timestamp": timestamp,
                    "open": float(open_price),  # 🔥 转换为float用于兼容性，但保持了Decimal的精度
                    "high": float(high_price),  # 🔥 转换为float用于兼容性，但保持了Decimal的精度
                    "low": float(low_price),    # 🔥 转换为float用于兼容性，但保持了Decimal的精度
                    "close": float(close_price), # 🔥 转换为float用于兼容性，但保持了Decimal的精度
                    "volume": float(volume)     # 🔥 转换为float用于兼容性，但保持了Decimal的精度
                })
            
            # 创建标准化的K线数据
            kline_data = {
                "exchange": "BYBIT",
                "symbol": symbol,
                "timestamp": int(time.time() * 1000),
                "klines": klines,
                "market_type": self.market_type
            }
            
            # 记录debug日志
            self._log_debug(f"K线 {symbol}: {len(klines)}根")
            
            # 触发回调
            self.emit("kline", kline_data)
            
        except Exception as e:
            self._log_error(f"处理K线数据出错: {str(e)}", exc_info=True)
    
    async def send_heartbeat(self):
        """发送心跳包"""
        heartbeat_msg = {"op": "ping"}
        if await self.send(heartbeat_msg):
            self._log_debug("发送心跳")
            return True
        return False

    def _update_stats(self, channel_type: str):
        """更新统计信息 - 🔥 修复版本：恢复统计功能"""
        if channel_type in self.message_stats:
            self.message_stats[channel_type] += 1
        else:
            self.message_stats["other"] += 1
        
        # 🔥 关键：记录Bybit数据接收情况
        current_time = time.time()
        if not hasattr(self, '_last_stats_log'):
            self._last_stats_log = 0
        
        # 每30秒记录一次统计
        if current_time - self._last_stats_log > 30:
            total_messages = sum(self.message_stats.values())
            self._log_info(f"📊 Bybit {self.market_type} 消息统计: 总计{total_messages}, {channel_type}+1")
            self._last_stats_log = current_time
    
    def _log_stats(self):
        """记录统计信息 - 🔥 修复版本：恢复日志统计"""
        current_time = time.time()
        if hasattr(self, 'last_stats_time'):
            interval = current_time - self.last_stats_time
            if interval >= self.stats_interval:
                total_messages = sum(self.message_stats.values())
                self._log_info(f"📈 Bybit {self.market_type} 统计: {total_messages}条消息 ({interval:.1f}秒)")
                self.last_stats_time = current_time

    def get_status(self):
        """获取WebSocket状态"""
        connected = self.ws is not None and self.ws.open if hasattr(self, 'ws') else False
        return {
            "connected": connected,
            "reconnect_count": self.reconnect_count,
            "message_count": sum(self.message_stats.values()),
            "subscriptions": list(self.active_subscriptions),
            "last_message_time": self.last_message_time
        }

    async def send(self, message):
        """
        发送WebSocket消息
        
        Args:
            message: 要发送的消息对象
            
        Returns:
            bool: 发送是否成功
        """
        if not self.ws or not self.ws.open:
            self._log_warning("WebSocket未连接，无法发送消息")
            return False
            
        try:
            message_str = json.dumps(message)
            await self.ws.send(message_str)
            self._log_debug(f"发送消息: {message_str[:100]}...")
            return True
        except Exception as e:
            self._log_error(f"发送消息失败: {str(e)}", exc_info=True)
            return False

    async def _subscribe_orderbook(self):
        """订阅orderbook更新 - 🔥 修复：分批订阅，避免超过10个限制"""
        if not self.symbols:
            return
        
        # 🔥 关键修复：限制每批最多10个，避免"args size >10"错误
        BATCH_SIZE = 8  # 保守一点，每批8个
        
        for i in range(0, len(self.symbols), BATCH_SIZE):
            batch_symbols = self.symbols[i:i + BATCH_SIZE]
            channels = [f"orderbook.50.{symbol}" for symbol in batch_symbols]  # 🔥 修复：使用50档（Bybit不支持10档）
            
            # 🔥 记录活跃订阅
            for channel in channels:
                self.active_subscriptions.add(channel)
            
            self._log_info(f"📊 订阅orderbook批次: {len(channels)}个频道")
            
            subscribe_msg = {
                "op": "subscribe",
                "args": channels
            }
            
            # 🔥 修复：使用正确的send方法
            success = await self.send(subscribe_msg)
            if not success:
                    self._log_error(f"❌ [BYBIT-{self.market_type.upper()}] 订阅orderbook失败: 批次 {i//BATCH_SIZE + 1}")
                    self._log_error(f"🔍 [BYBIT-{self.market_type.upper()}] 失败详情: 交易对={batch_symbols}")
                    self._log_error(f"🔍 [BYBIT-{self.market_type.upper()}] 失败详情: 频道={channels}")
                    self._log_error(f"🔍 [BYBIT-{self.market_type.upper()}] 失败详情: 订阅消息={subscribe_msg}")

                    # 🔥 新增：记录订阅失败日志，包含详细的交易对信息
                    from .websocket_logger import log_websocket_subscription_failure
                    log_websocket_subscription_failure("error", f"orderbook批次订阅失败",
                                                     exchange="bybit",
                                                     market_type=self.market_type,
                                                     batch_number=i//BATCH_SIZE + 1,
                                                     symbols=batch_symbols,
                                                     failed_channels=channels,
                                                     subscribe_message=subscribe_msg)
            
            # 🔥 **官方API规范修复**：基于Bybit官方文档频率限制
            # Bybit官方：600 requests/5s per IP，即平备0.0083s/request
            if i + BATCH_SIZE < len(self.symbols):  # 🔥 **CRITICAL修复**: 使用self.symbols而不是未定义的symbols
                await asyncio.sleep(0.1)  # **修复：改为0.1秒，符合Bybit官方频率要求**

    # 🔥 TICKER订阅方法已完全删除 - 系统只使用OrderBook数据

    # 🚀 已删除：_subscribe_trade() 方法 - 套利系统不需要成交数据


async def test_bybit_websocket():
    """测试Bybit WebSocket连接"""
    # 创建Bybit WebSocket客户端
    client = BybitWebSocketClient("spot")
    
    # 设置交易对
    client.set_symbols(["BTCUSDT", "ETHUSDT"])
    
    # 注册回调函数
    async def on_orderbook(data):
        # 处理订单簿数据
        pass

    # ticker处理已完全移除

    client.register_callback("orderbook", on_orderbook)
    
    # 启动客户端
    try:
        await client.run()
    except KeyboardInterrupt:
        pass  # 用户中断，关闭连接
    finally:
        await client.close()


if __name__ == "__main__":
    # 配置简单的日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout)
        ]
    )
    
    # 运行测试
    asyncio.run(test_bybit_websocket())