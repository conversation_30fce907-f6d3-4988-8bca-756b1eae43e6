#!/usr/bin/env python3
"""
重复阻塞检测函数冲突精确诊断脚本
专门检测和分析重复阻塞检测导致的系统冲突

根据初步诊断结果：
- 发现5个文件都有阻塞检测逻辑：ws_client.py, gate_ws.py, bybit_ws.py, okx_ws.py, enhanced_blocking_tracker.py
- 数据堆积1939秒（32分钟），严重违反<30ms性能要求
- 需要精确定位冲突点和修复方案
"""

import sys
import os
import re
import json
from pathlib import Path
from datetime import datetime

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def analyze_blocking_detection_functions():
    """分析所有文件中的阻塞检测函数"""
    print("🔍 精确分析：重复阻塞检测函数冲突")
    print("=" * 60)
    
    # 要检查的文件列表
    files_to_check = [
        "websocket/enhanced_blocking_tracker.py",  # 应该是唯一的阻塞检测器
        "websocket/ws_client.py",                  # WebSocket基类
        "websocket/gate_ws.py",                    # Gate.io WebSocket
        "websocket/bybit_ws.py",                   # Bybit WebSocket  
        "websocket/okx_ws.py"                      # OKX WebSocket
    ]
    
    blocking_functions = {}
    
    for file_path in files_to_check:
        full_path = project_root / file_path
        if not full_path.exists():
            print(f"⚠️ 文件不存在: {file_path}")
            continue
            
        with open(full_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检测阻塞相关的函数和逻辑
        functions_found = []
        
        # 检测函数定义
        blocking_patterns = [
            r'def.*monitor.*data.*flow',
            r'def.*detect.*blocking',
            r'def.*handle.*blocking',
            r'def.*check.*staleness',
            r'def.*log.*staleness',
            r'def.*silent.*disconnect'
        ]
        
        for pattern in blocking_patterns:
            matches = re.findall(pattern, content, re.IGNORECASE)
            functions_found.extend(matches)
        
        # 检测关键变量和逻辑
        blocking_indicators = []
        
        if 'silent_duration' in content:
            blocking_indicators.append('silent_duration变量')
        if 'data_flow_timeout' in content:
            blocking_indicators.append('data_flow_timeout变量')
        if 'last_data_time' in content:
            blocking_indicators.append('last_data_time变量')
        if '_monitor_data_flow' in content:
            blocking_indicators.append('_monitor_data_flow方法')
        if 'blocking_event' in content:
            blocking_indicators.append('blocking_event处理')
        
        blocking_functions[file_path] = {
            'functions': functions_found,
            'indicators': blocking_indicators,
            'has_blocking_logic': len(functions_found) > 0 or len(blocking_indicators) > 0
        }
    
    return blocking_functions

def analyze_conflict_severity(blocking_functions):
    """分析冲突严重程度"""
    print("\n🚨 冲突严重程度分析")
    print("=" * 40)
    
    files_with_blocking = []
    total_functions = 0
    total_indicators = 0
    
    for file_path, data in blocking_functions.items():
        if data['has_blocking_logic']:
            files_with_blocking.append(file_path)
            total_functions += len(data['functions'])
            total_indicators += len(data['indicators'])
            
            print(f"\n📁 {file_path}:")
            if data['functions']:
                print(f"  🔧 函数: {len(data['functions'])}个")
                for func in data['functions']:
                    print(f"    - {func}")
            if data['indicators']:
                print(f"  ⚡ 指标: {len(data['indicators'])}个")
                for indicator in data['indicators']:
                    print(f"    - {indicator}")
    
    print(f"\n📊 冲突统计:")
    print(f"  有阻塞逻辑的文件: {len(files_with_blocking)}个")
    print(f"  总阻塞函数数量: {total_functions}个")
    print(f"  总阻塞指标数量: {total_indicators}个")
    
    # 判断冲突严重程度
    if len(files_with_blocking) > 1:
        severity = "CRITICAL" if len(files_with_blocking) > 3 else "HIGH"
        print(f"  🚨 冲突严重程度: {severity}")
        print(f"  💥 预期影响: 系统性能严重下降，消息队列积压")
    else:
        print(f"  ✅ 冲突严重程度: LOW")
    
    return files_with_blocking, severity if len(files_with_blocking) > 1 else "LOW"

def generate_fix_recommendations(blocking_functions, files_with_blocking):
    """生成修复建议"""
    print("\n🔧 精确修复建议")
    print("=" * 40)
    
    # enhanced_blocking_tracker应该是唯一的阻塞检测器
    enhanced_tracker = "websocket/enhanced_blocking_tracker.py"
    
    if enhanced_tracker in files_with_blocking:
        print(f"✅ 保留: {enhanced_tracker} (唯一的阻塞检测器)")
        files_to_clean = [f for f in files_with_blocking if f != enhanced_tracker]
    else:
        print(f"⚠️ 警告: {enhanced_tracker} 不在阻塞检测文件列表中")
        files_to_clean = files_with_blocking
    
    if files_to_clean:
        print(f"\n🧹 需要清理的文件: {len(files_to_clean)}个")
        
        for file_path in files_to_clean:
            data = blocking_functions[file_path]
            print(f"\n📁 {file_path}:")
            print(f"  🗑️ 需要移除的函数: {len(data['functions'])}个")
            for func in data['functions']:
                print(f"    - {func}")
            print(f"  🗑️ 需要移除的逻辑: {len(data['indicators'])}个")
            for indicator in data['indicators']:
                print(f"    - {indicator}")
    
    return files_to_clean

def create_fix_script_template(files_to_clean):
    """创建修复脚本模板"""
    print("\n📝 生成修复脚本模板")
    print("=" * 40)
    
    fix_script = f"""#!/usr/bin/env python3
'''
重复阻塞检测函数清理脚本
自动生成于: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

修复目标: 确保只有enhanced_blocking_tracker处理阻塞检测
'''

import os
import re
from pathlib import Path

project_root = Path(__file__).parent.parent

def clean_blocking_logic():
    '''清理重复的阻塞检测逻辑'''
    
    files_to_clean = {files_to_clean}
    
    for file_path in files_to_clean:
        full_path = project_root / file_path
        if not full_path.exists():
            continue
            
        print(f"🧹 清理文件: {{file_path}}")
        
        with open(full_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 备份原文件
        backup_path = full_path.with_suffix(full_path.suffix + '.backup')
        with open(backup_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        # TODO: 添加具体的清理逻辑
        # 这里需要根据每个文件的具体情况进行清理
        
        print(f"✅ 已备份到: {{backup_path}}")

if __name__ == "__main__":
    clean_blocking_logic()
"""
    
    # 保存修复脚本模板
    fix_script_path = project_root / "diagnostic_scripts" / "fix_blocking_conflicts.py"
    with open(fix_script_path, 'w', encoding='utf-8') as f:
        f.write(fix_script)
    
    print(f"📄 修复脚本模板已生成: {fix_script_path}")
    return fix_script_path

def generate_detailed_report(blocking_functions, files_with_blocking, severity):
    """生成详细诊断报告"""
    print("\n📋 生成详细诊断报告")
    print("=" * 40)
    
    report = {
        "diagnosis_time": datetime.now().isoformat(),
        "problem_type": "重复阻塞检测函数冲突",
        "severity": severity,
        "performance_impact": {
            "expected_latency": "<30ms",
            "actual_latency": "1939秒 (Gate.io), 1945秒 (OKX)",
            "performance_degradation": "99.998%"
        },
        "conflict_analysis": {
            "files_with_blocking_logic": len(files_with_blocking),
            "total_blocking_functions": sum(len(data['functions']) for data in blocking_functions.values()),
            "total_blocking_indicators": sum(len(data['indicators']) for data in blocking_functions.values())
        },
        "detailed_findings": blocking_functions,
        "root_cause": "多个文件同时进行阻塞检测，导致系统资源冲突和消息队列积压",
        "fix_strategy": "保留enhanced_blocking_tracker作为唯一阻塞检测器，清理其他文件中的重复逻辑",
        "expected_improvement": "恢复<30ms延迟性能，消除消息队列积压"
    }
    
    # 保存详细报告
    report_path = project_root / "diagnostic_scripts" / f"blocking_conflict_diagnosis_{int(datetime.now().timestamp())}.json"
    with open(report_path, 'w', encoding='utf-8') as f:
        json.dump(report, f, indent=2, ensure_ascii=False)
    
    print(f"📄 详细报告已保存: {report_path}")
    return report

def main():
    """主函数"""
    print("🚨 重复阻塞检测函数冲突精确诊断")
    print("=" * 70)
    print("目标: 精确定位重复阻塞检测导致的1939秒延迟问题")
    print()
    
    # 分析阻塞检测函数
    blocking_functions = analyze_blocking_detection_functions()
    
    # 分析冲突严重程度
    files_with_blocking, severity = analyze_conflict_severity(blocking_functions)
    
    # 生成修复建议
    files_to_clean = generate_fix_recommendations(blocking_functions, files_with_blocking)
    
    # 创建修复脚本模板
    fix_script_path = create_fix_script_template(files_to_clean)
    
    # 生成详细报告
    report = generate_detailed_report(blocking_functions, files_with_blocking, severity)
    
    print(f"\n🎯 诊断结论:")
    print(f"发现 {len(files_with_blocking)} 个文件有阻塞检测逻辑")
    print(f"冲突严重程度: {severity}")
    print(f"需要清理 {len(files_to_clean)} 个文件")
    print(f"预期修复后性能提升: 从1939秒恢复到<30ms")

if __name__ == "__main__":
    main()
