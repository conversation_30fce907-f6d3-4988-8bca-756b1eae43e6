#!/usr/bin/env python3
'''
重复阻塞检测函数清理脚本
自动生成于: 2025-08-05 19:25:51

修复目标: 确保只有enhanced_blocking_tracker处理阻塞检测
'''

import os
import re
from pathlib import Path

project_root = Path(__file__).parent.parent

def clean_blocking_logic():
    '''清理重复的阻塞检测逻辑'''
    
    files_to_clean = ['websocket/ws_client.py', 'websocket/gate_ws.py', 'websocket/bybit_ws.py', 'websocket/okx_ws.py']
    
    for file_path in files_to_clean:
        full_path = project_root / file_path
        if not full_path.exists():
            continue
            
        print(f"🧹 清理文件: {file_path}")
        
        with open(full_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 备份原文件
        backup_path = full_path.with_suffix(full_path.suffix + '.backup')
        with open(backup_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        # TODO: 添加具体的清理逻辑
        # 这里需要根据每个文件的具体情况进行清理
        
        print(f"✅ 已备份到: {backup_path}")

if __name__ == "__main__":
    clean_blocking_logic()
